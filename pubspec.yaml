name: lavamail
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  provider: ^6.0.5
  flutter_svg: ^2.0.7
  timezone: ^0.10.0
  url_launcher: ^6.2.5

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  firebase_core: ^3.13.0
  firebase_auth: ^5.5.3
  google_sign_in: ^6.3.0
  http: ^1.1.0
  googleapis: ^14.0.0
  googleapis_auth: ^2.0.0
  logger: ^2.0.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  path_provider: ^2.1.5
  percent_indicator: ^4.2.3
  archive: ^4.0.7
  connectivity_plus: ^6.1.4
  hive_generator: ^2.0.1
  build_runner: ^2.4.15
  intl: ^0.19.0
  flutter_localizations:
    sdk: flutter
  flutter_local_notifications: ^19.2.1
  country_flags: ^3.3.0
  flutter_secure_storage: ^9.0.0
  crypto: ^3.0.3
  html: ^0.15.4
  encrypt: ^5.0.3
  google_mobile_ads: ^6.0.0
  # GMX integration dependencies
  enough_mail: ^2.1.6

dev_dependencies:
  flutter_test:
    sdk: flutter
  mockito: ^5.4.4
  test: ^1.24.9
  fake_async: ^1.3.1
  mocktail: ^1.0.3

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  uses-material-design: true
  assets:
    - assets/images/
  generate: true

l10n:
  arb-dir: lib/Internationalization
