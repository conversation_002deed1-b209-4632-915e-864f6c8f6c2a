# Language and Naming Conventions
- Write ALL code in English only
- Use English for variable names, function names, class names
- Use English for comments and documentation
- Use English for error messages and console logs
- Use English for API endpoints and database field names

# Code Standards
- Use descriptive English variable names (e.g., `userProfile` not `profilUtilisateur`)
- Write comments in English
- Use English for commit messages
- Documentation must be in English
- Test descriptions in English

# LavaMail Project Structure - STRICTLY FOLLOW
lib/
├── main.dart                # App entry point, initializes app, theme, locale, navigation
├── l10n/                    # Localization files (.arb) for each supported language
├── utils/
│   ├── helpers/             # Utility functions (encryption, validation, email list, perf tests)
│   └── settings/            # Centralized user settings management (language, sync, notifications, etc.)
├── fronted/
│   ├── home_screen/         # Inbox, labels, spam, unsubscribe, sync, and profile screens
│   ├── settings_screen/     # User settings screen
│   ├── widgets/             # Reusable UI widgets (buttons, lists, categories)
│   ├── login_screen/        # Google login screen
│   └── theme/               # App theming and styles
├── core/
│   └── gmail/
│       ├── common_functions/ # Shared Gmail logic (sync engine, optimizers, API, performance)
│       ├── offline_mode/     # Offline data management and hybrid mode
│       └── online_mode/      # Online sync, watcher, notifications, Gmail API services

# Directory Roles - RESPECT THESE STRICTLY
- l10n/: Internationalization (i18n) for all UI text ONLY
- utils/helpers/: Pure Dart helpers for logic, security, and data manipulation ONLY
- utils/settings/: All persistent user/app settings (Hive-based) ONLY
- fronted/home_screen/: All main Gmail-related screens (inbox, labels, spam, etc.) ONLY
- fronted/settings_screen/: The main settings UI for the user ONLY
- fronted/widgets/: UI components reused across screens ONLY
- fronted/login_screen/: Google authentication screens ONLY
- fronted/theme/: App theming and styles ONLY
- core/gmail/common_functions/: Core Gmail sync and optimization logic ONLY
- core/gmail/offline_mode/: Services for offline/hybrid data access ONLY
- core/gmail/online_mode/: Services for online sync, notifications, and Gmail API ONLY

# File Placement Rules
- NEVER place files in wrong directories
- Always suggest correct directory based on file purpose
- If unsure about placement, ask for clarification
- Maintain strict separation of concerns per directory
