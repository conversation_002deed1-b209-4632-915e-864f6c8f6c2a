import imaplib
import ssl
import re

def parse_folder_info(folder_line):
    """Parse a line of IMAP folder info"""
    # Typical format: (flags) "separator" "folder_name"
    match = re.match(r'\(([^)]*)\)\s+"([^"]*)"\s+"([^"]*)"', folder_line)
    if match:
        flags = match.group(1)
        separator = match.group(2)
        name = match.group(3)
        return {
            'flags': flags.split() if flags else [],
            'separator': separator,
            'name': name,
            'raw': folder_line
        }
    return {'raw': folder_line, 'name': folder_line}

def detailed_folder_discovery(email, password):
    print("Attempting to connect...")
    
    # Method 1: SSL with verification disabled
    try:
        print("Method 1: Standard SSL with verification disabled")
        context = ssl.create_default_context()
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE
        
        mail = imaplib.IMAP4_SSL('imap.gmx.net', 993, ssl_context=context)
        mail.login(email, password)
        print("✓ Connection successful!")
        
    except Exception as e1:
        print(f"✗ Method 1 failed: {e1}")
        
        # Method 2: STARTTLS on port 143
        try:
            print("Method 2: STARTTLS on port 143")
            mail = imaplib.IMAP4('imap.gmx.net', 143)
            mail.starttls()
            mail.login(email, password)
            print("✓ Connection successful with STARTTLS!")
            
        except Exception as e2:
            print(f"✗ Method 2 failed: {e2}")
            
            # Method 3: No SSL (not recommended, just for testing)
            try:
                print("Method 3: Non-SSL connection (test only)")
                mail = imaplib.IMAP4('imap.gmx.net', 143)
                mail.login(email, password)
                print("✓ Connection successful without SSL!")
                
            except Exception as e3:
                print(f"✗ All connection methods failed")
                print(f"SSL Error: {e1}")
                print(f"STARTTLS Error: {e2}")
                print(f"Non-SSL Error: {e3}")
                return
    
    # If we reach here, a connection was successful
    try:
        status, folders = mail.list()
        if status != 'OK' or folders is None:
            print("Failed to retrieve folders.")
            mail.logout()
            return
        print("=== DETAILED GMX FOLDER ANALYSIS ===")
        print(f"Total number of folders: {len(folders)}\n")
        for folder in folders:
            if isinstance(folder, bytes):
                folder_str = folder.decode('utf-8')
            else:
                folder_str = str(folder)
            parsed = parse_folder_info(folder_str)
            print(f"Name: '{parsed['name']}'")
            if 'flags' in parsed:
                print(f"Flags: {parsed['flags']}")
            print(f"Raw: {parsed['raw']}")
            print("-" * 50)
        mail.logout()
    except Exception as e:
        print(f"Error while retrieving folders: {e}")


# ======= ENTER YOUR CREDENTIALS HERE =======
EMAIL = "<EMAIL>"        # ← Replace with your GMX email
PASSWORD = "I1puoX7OKNITYx5"              # ← Replace with your password

# Script entry point
if __name__ == "__main__":
    detailed_folder_discovery(EMAIL, PASSWORD)