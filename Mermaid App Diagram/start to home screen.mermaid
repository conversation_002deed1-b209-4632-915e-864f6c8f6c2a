flowchart TD
    A["🚀 main.dart"] --> B["Initialization"]
    B --> C["Firebase Init"] & D["Hive Init"] & E["Encryption Init"] & F["UserProvider Init"] & G1["⚙️ Loading settings options"]
    F --> G["MyApp Widget"]
    G1 --> G
    G --> H1["🌐 Android OS language detection"]
    H1 --> H2{"Android OS language supported?"}
    H2 -- ✅ Yes --> H3["📱 Loading Android OS language"]
    H2 -- ❌ No --> H4["🇬🇧 Loading English language"]
    H3 --> H["🌟 SplashScreen"]
    H4 --> H
    H --> I["🔐 LoginScreen"]
    I --> J["Email provider selection"]
    J --> K{"Provider choice"}
    K -- Gmail --> L1["🔍 Gmail credentials verification"]
    K -- GMX --> M1["🔍 GMX credentials verification"]
    K -- Yahoo --> N1["🔍 Yahoo credentials verification"]
    K -- iCloud --> O1["🔍 iCloud credentials verification"]
    L1 --> L2{"Gmail credentials saved?"}
    M1 --> M2{"GMX credentials saved?"}
    N1 --> N2{"Yahoo credentials saved?"}
    O1 --> O2{"iCloud credentials saved?"}
    L2 -- ✅ Yes --> L["📧 Pre-filled Gmail form"]
    L2 -- ❌ No --> L3["📧 Empty Gmail form"]
    M2 -- ✅ Yes --> M["📧 Pre-filled GMX form"]
    M2 -- ❌ No --> M3["📧 Empty GMX form"]
    N2 -- ✅ Yes --> N["📧 Pre-filled Yahoo form"]
    N2 -- ❌ No --> N3["📧 Empty Yahoo form"]
    O2 -- ✅ Yes --> O["📧 Pre-filled iCloud form"]
    O2 -- ❌ No --> O3["📧 Empty iCloud form"]
    L --> L4["👤 Visible login + 🔒 Hidden password"]
    M --> M4["👤 Visible login + 🔒 Hidden password"]
    N --> N4["👤 Visible login + 🔒 Hidden password"]
    O --> O4["👤 Visible login + 🔒 Hidden password"]
    L4 --> L5["🔘 Click Gmail connection button"]
    M4 --> M5["🔘 Click GMX connection button"]
    N4 --> N5["🔘 Click Yahoo connection button"]
    O4 --> O5["🔘 Click iCloud connection button"]
    L3 --> L6["✏️ Manual Gmail entry"]
    M3 --> M6["✏️ Manual GMX entry"]
    N3 --> N6["✏️ Manual Yahoo entry"]
    O3 --> O6["✏️ Manual iCloud entry"]
    L6 --> L5
    M6 --> M5
    N6 --> N5
    O6 --> O5
    L5 --> P["Gmail OAuth2 authentication"]
    M5 --> Q["GMX IMAP authentication"]
    N5 --> R["Yahoo IMAP authentication"]
    O5 --> S["iCloud IMAP authentication"]
    P --> T{"Gmail OAuth2 successful?"}
    Q --> U{"GMX IMAP auth successful?"}
    R --> V{"Yahoo IMAP auth successful?"}
    S --> W{"iCloud IMAP auth successful?"}
    T -- ✅ Yes --> X["UserProvider update - Gmail (OAuth2)"]
    T -- ❌ No --> Y["❌ Gmail OAuth2 error"]
    Y --> L4
    U -- ✅ Yes --> Z["UserProvider update - GMX (IMAP)"]
    U -- ❌ No --> AA["❌ GMX IMAP error + App Password info"]
    AA --> M4
    V -- ✅ Yes --> BB["UserProvider update - Yahoo (IMAP)"]
    V -- ❌ No --> CC["❌ Yahoo IMAP error"]
    CC --> N4
    W -- ✅ Yes --> DD["UserProvider update - iCloud (IMAP)"]
    W -- ❌ No --> EE["❌ iCloud IMAP error"]
    EE --> O4
    X --> X1["💾 Save Gmail credentials"]
    Z --> Z1["💾 Save GMX credentials"]
    BB --> BB1["💾 Save Yahoo credentials"]
    DD --> DD1["💾 Save iCloud credentials"]
    X1 --> X2["📊 Start Gmail data loading (API)"]
    Z1 --> Z2["📊 Start GMX data loading (IMAP)"]
    BB1 --> BB2["📊 Start Yahoo data loading (IMAP)"]
    DD1 --> DD2["📊 Start iCloud data loading (IMAP)"]
    X2 --> X3["📧 Gmail metadata loading (API)"]
    Z2 --> Z3["📧 GMX metadata loading (IMAP)"]
    BB2 --> BB3["📧 Yahoo metadata loading (IMAP)"]
    DD2 --> DD3["📧 iCloud metadata loading (IMAP)"]
    X3 --> X4["📁 Gmail folders/labels loading (API)"]
    Z3 --> Z4["📁 GMX folders/labels loading (IMAP)"]
    BB3 --> BB4["📁 Yahoo folders/labels loading (IMAP)"]
    DD3 --> DD4["📁 iCloud folders/labels loading (IMAP)"]
    X4 --> X5["📎 Gmail attachments counting (API)"]
    Z4 --> Z5["📎 GMX attachments counting (IMAP)"]
    BB4 --> BB5["📎 Yahoo attachments counting (IMAP)"]
    DD4 --> DD5["📎 iCloud attachments counting (IMAP)"]
    X5 --> FF["🏠 Navigate to HomeScreen"]
    Z5 --> FF
    BB5 --> FF
    DD5 --> FF
    FF --> GG["🏠 HomeScreen"]
    GG --> LL1["🧭 Menu Widget Loading"] & HH1["👤 Profile Widget Loading"] & II1["📁 Categories Widget Loading"] & JJ1["📊 Informations Widget Loading"]
    LL1 --> LL2["🏠 Home button"] & LL3["📊 Stats button"] & LL4["⚙️ Settings button"]
    LL2 --> LL5["✅ Menu Widget Ready"]
    LL3 --> LL5
    LL4 --> LL5
    HH1 --> HH2{"Profile photo available?"}
    HH2 -- ✅ Yes --> HH3["📸 Display user profile photo"]
    HH2 -- ❌ No --> HH4["👤 Display generic profile photo"]
    HH3 --> HH5{"User name available?"}
    HH4 --> HH5
    HH5 -- ✅ Yes --> HH6["📝 Display name + email"]
    HH5 -- ❌ No --> HH7["📧 Display email only"]
    HH6 --> HH8["✅ Profile Widget Ready"]
    HH7 --> HH8
    II1 --> II2["🔍 Fetch all account categories"]
    II2 --> II3["📊 Count emails per category"]
    II3 --> II4["📁 Display categories with counts"]
    II4 --> II5["✅ Categories Widget Ready"]
    JJ1 --> JJ2["📧 Count total emails in account"] & JJ3["📎 Count total attachments in account"]
    JJ2 --> JJ4["📊 Display total email count"]
    JJ3 --> JJ5["📎 Display total attachment count"]
    JJ4 --> JJ6["✅ Informations Widget Ready"]
    JJ5 --> JJ6
    LL5 --> KK["🎉 HomeScreen Fully Loaded"]
    HH8 --> KK
    II5 --> KK
    JJ6 --> KK

    G1:::settings
    H1:::language
    H2:::language
    H3:::language
    H4:::language
    L1:::credentials
    M1:::credentials
    N1:::credentials
    O1:::credentials
    L2:::credentials
    M2:::credentials
    N2:::credentials
    O2:::credentials
    L:::formModal
    L3:::formModal
    M:::formModal
    M3:::formModal
    N:::formModal
    N3:::formModal
    O:::formModal
    O3:::formModal
    L4:::formPrefilled
    M4:::formPrefilled
    N4:::formPrefilled
    O4:::formPrefilled
    L5:::userAction
    M5:::userAction
    N5:::userAction
    O5:::userAction
    L6:::userAction
    M6:::userAction
    N6:::userAction
    O6:::userAction
    P:::testConnection
    Q:::imapAuth
    R:::imapAuth
    S:::imapAuth
    X:::success
    Y:::errorMsg
    Z:::imapSuccess
    AA:::errorMsg
    BB:::imapSuccess
    CC:::errorMsg
    DD:::imapSuccess
    EE:::errorMsg
    X1:::credentials
    Z1:::credentials
    BB1:::credentials
    DD1:::credentials
    X2:::dataLoading
    Z2:::dataLoading
    BB2:::dataLoading
    DD2:::dataLoading
    X3:::dataLoading
    Z3:::dataLoading
    BB3:::dataLoading
    DD3:::dataLoading
    X4:::dataLoading
    Z4:::dataLoading
    BB4:::dataLoading
    DD4:::dataLoading
    X5:::dataLoading
    Z5:::dataLoading
    BB5:::dataLoading
    DD5:::dataLoading
    LL1:::widgetLoading
    HH1:::widgetLoading
    II1:::widgetLoading
    JJ1:::widgetLoading
    LL2:::menuWidget
    LL3:::menuWidget
    LL4:::menuWidget
    LL5:::widgetReady
    HH2:::widgetLoading
    HH3:::profileWidget
    HH4:::profileWidget
    HH5:::widgetLoading
    HH6:::profileWidget
    HH7:::profileWidget
    HH8:::widgetReady
    II2:::categoriesWidget
    II3:::categoriesWidget
    II4:::categoriesWidget
    II5:::widgetReady
    JJ2:::informationsWidget
    JJ3:::informationsWidget
    JJ4:::informationsWidget
    JJ5:::informationsWidget
    JJ6:::widgetReady
    KK:::homeComplete
    classDef formModal fill:#dda0dd,stroke:#9370db
    classDef testConnection fill:#87ceeb,stroke:#4682b4
    classDef errorMsg fill:#ffb3ba,stroke:#ff6b6b
    classDef provider fill:#98d8c8,stroke:#06d6a0
    classDef service fill:#f7dc6f,stroke:#f1c40f
    classDef success fill:#90ee90,stroke:#32cd32
    classDef language fill:#ffd93d,stroke:#ffb700
    classDef credentials fill:#a8e6cf,stroke:#7fcdcd
    classDef dataLoading fill:#ffaaa5,stroke:#ff8b94
    classDef settings fill:#b19cd9,stroke:#8e44ad
    classDef formPrefilled fill:#ffeaa7,stroke:#fdcb6e
    classDef userAction fill:#74b9ff,stroke:#0984e3
    classDef widgetLoading fill:#e17055,stroke:#d63031
    classDef profileWidget fill:#fd79a8,stroke:#e84393
    classDef categoriesWidget fill:#fdcb6e,stroke:#e17055
    classDef informationsWidget fill:#81ecec,stroke:#00cec9
    classDef widgetReady fill:#00b894,stroke:#00a085
    classDef homeComplete fill:#6c5ce7,stroke:#5f3dc4
    classDef menuWidget fill:#a29bfe,stroke:#6c5ce7
    classDef navigation fill:#fab1a0,stroke:#e17055
    classDef screen fill:#55a3ff,stroke:#2d3436
    classDef imapAuth fill:#ff9f43,stroke:#e17055
    classDef imapSuccess fill:#00b894,stroke:#00a085
    style A fill:#ff6b6b
    style H fill:#4ecdc4
    style I fill:#45b7d1
    style GG fill:#96ceb4
    style KK fill:#6c5ce7