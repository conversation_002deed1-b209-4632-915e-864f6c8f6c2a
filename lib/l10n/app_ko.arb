{"@@locale": "ko", "appTitle": "<PERSON><PERSON>", "@appTitle": {"description": "The title of the application"}, "settings": "Settings", "@settings": {"description": "Settings menu label"}, "sync": "Synchronization", "@sync": {"description": "Synchronization label"}, "language": "Language", "@language": {"description": "Language selection label"}, "english": "English", "@english": {"description": "English language option"}, "french": "French", "@french": {"description": "French language option"}, "german": "German", "@german": {"description": "German language option"}, "spanish": "Spanish", "@spanish": {"description": "Spanish language option"}, "portuguese": "Portuguese", "@portuguese": {"description": "Portuguese language option"}, "hindi": "Hindi", "@hindi": {"description": "Hindi language option"}, "indonesian": "Indonesian", "@indonesian": {"description": "Indonesian language option"}, "japanese": "Japanese", "@japanese": {"description": "Japanese language option"}, "italian": "Italian", "@italian": {"description": "Italian language option"}, "notifications": "Notifications", "@notifications": {"description": "Notifications settings label"}, "manualSync": "Manual sync now", "@manualSync": {"description": "But<PERSON> to trigger manual synchronization"}, "syncFrequency": "Sync frequency", "@syncFrequency": {"description": "Synchronization frequency setting label"}, "wifiOnly": "Sync on Wi-Fi only", "@wifiOnly": {"description": "Option to sync only when connected to Wi-Fi"}, "syncMode": "Sync mode", "@syncMode": {"description": "Synchronization mode setting label"}, "auto": "Auto", "@auto": {"description": "Automatic synchronization mode"}, "manual": "Manual", "@manual": {"description": "Manual synchronization mode"}, "dataMode": "Data Mode", "@dataMode": {"description": "Data access mode setting label"}, "offlineMode": "Offline", "@offlineMode": {"description": "Offline data mode"}, "onlineMode": "Online", "@onlineMode": {"description": "Online data mode"}, "offlineModeDesc": "Use local cache only. Faster but may not be up to date.", "@offlineModeDesc": {"description": "Description of offline mode"}, "onlineModeDesc": "Use Gmail API directly. Always up to date but slower.", "@onlineModeDesc": {"description": "Description of online mode"}, "massDelete": "Mass Delete", "@massDelete": {"description": "Mass delete action"}, "massUnsubscribe": "Mass Unsubscribe", "@massUnsubscribe": {"description": "Mass unsubscribe action"}, "sortBySender": "Sort by Sender", "@sortBySender": {"description": "Sort emails by sender"}, "sortByDate": "Sort by Date", "@sortByDate": {"description": "Sort emails by date"}, "emailSize": "<PERSON><PERSON>", "@emailSize": {"description": "Email size label"}, "totalSize": "Total Size", "@totalSize": {"description": "Total size label"}, "averageSize": "Average Size", "@averageSize": {"description": "Average size label"}, "clearCache": "Clear cache and database, then resynchronize", "@clearCache": {"description": "Button to clear cache and resynchronize"}, "languageChanged": "Language changed successfully", "@languageChanged": {"description": "Success message when language is changed"}, "selectLanguage": "Select your preferred language", "@selectLanguage": {"description": "Instruction to select preferred language"}, "syncSettings": "Synchronization Settings", "@syncSettings": {"description": "Synchronization settings section title"}, "generalSettings": "General Settings", "@generalSettings": {"description": "General settings section title"}, "simpleSecureEmail": "Simple and Secure Email", "@simpleSecureEmail": {"description": "App tagline or description"}, "signInCancelled": "Sign in was cancelled", "@signInCancelled": {"description": "Message when user cancels sign in"}, "signInFailed": "Failed to sign in", "@signInFailed": {"description": "Error message when sign in fails"}, "signingIn": "Signing in...", "@signingIn": {"description": "Loading message during sign in process"}, "continueWithGmail": "Continue with Gmail", "@continueWithGmail": {"description": "Button to continue with Gmail authentication"}, "appLogo": "App logo", "@appLogo": {"description": "Accessibility label for app logo"}, "signInWithGmail": "Gmail로 로그인", "@signInWithGmail": {"description": "<PERSON><PERSON> to sign in with Gmail"}, "signInWithWeb": "웹으로 로그인", "webAuthDescription": "웹 브라우저를 사용하여 로그인", "openingBrowser": "브라우저 열기...", "webAuthFailed": "웹 인증 실패", "webAuthCancelled": "웹 인증이 취소됨", "chooseSignInMethod": "로그인 방법을 선택하세요", "tapToSignIn": "로그인하려면 탭하세요", "inbox": "받은편지함", "@inbox": {"description": "Inbox folder label"}, "loading": "Loading...", "@loading": {"description": "Generic loading message"}, "emails": "emails", "@emails": {"description": "Plural form of email"}, "spam": "Spam", "@spam": {"description": "Spam folder label"}, "spams": "spams", "@spams": {"description": "Plural form of spam"}, "labels": "Labels", "@labels": {"description": "Labels section title"}, "checkingDatabase": "Checking database...", "@checkingDatabase": {"description": "Message when checking local database"}, "offlineProcessing": "Offline processing", "@offlineProcessing": {"description": "Label for offline processing phase"}, "overallProgress": "Overall progress", "@overallProgress": {"description": "Label for overall synchronization progress"}, "calculatingTime": "Calculating remaining time...", "@calculatingTime": {"description": "Message when calculating estimated time remaining"}, "retry": "Retry", "@retry": {"description": "Button to retry an operation"}, "logout": "Logout", "@logout": {"description": "Button to logout from the application"}, "emailCount": "{count} {count, plural, =0{emails} =1{email} other{emails}}", "@emailCount": {"description": "Displays the count of emails with proper pluralization", "placeholders": {"count": {"type": "int", "description": "Number of emails"}}}, "spamCount": "{count} {count, plural, =0{spams} =1{spam} other{spams}}", "@spamCount": {"description": "Displays the count of spam emails with proper pluralization", "placeholders": {"count": {"type": "int", "description": "Number of spam emails"}}}, "delete": "Delete", "@delete": {"description": "Delete button label"}, "cancel": "Cancel", "@cancel": {"description": "Cancel button label"}, "confirm": "Confirm", "@confirm": {"description": "Confirm button label"}, "deleteSelectedMessages": "Delete selected messages?", "@deleteSelectedMessages": {"description": "Title for delete confirmation dialog"}, "deleteConfirmationMessage": "Are you sure you want to permanently delete the selected messages? This action cannot be undone.", "@deleteConfirmationMessage": {"description": "Confirmation message for deleting messages"}, "deleteLabel": "Delete Label", "@deleteLabel": {"description": "Title for delete label dialog"}, "deleteLabelConfirmation": "Are you sure you want to delete the label \"{labelName}\" and all associated emails? This action cannot be undone.", "@deleteLabelConfirmation": {"description": "Confirmation message for deleting a label", "placeholders": {"labelName": {"type": "String", "description": "Name of the label to delete"}}}, "messagesDeletedSuccessfully": "{count} {count, plural, =1{message} other{messages}} deleted successfully", "@messagesDeletedSuccessfully": {"description": "Success message after deleting messages", "placeholders": {"count": {"type": "int", "description": "Number of deleted messages"}}}, "synchronizationCompleted": "Synchronization completed!", "@synchronizationCompleted": {"description": "Message when synchronization is finished"}, "errorDuringLogout": "Error during logout", "@errorDuringLogout": {"description": "Error message when logout fails"}, "noInboxMessages": "No inbox messages found", "@noInboxMessages": {"description": "Message when inbox is empty"}, "groupBy": "Group: {groupType}", "@groupBy": {"description": "Label for grouping dropdown", "placeholders": {"groupType": {"type": "String", "description": "Type of grouping"}}}, "descending": "Descending", "@descending": {"description": "Descending sort order option"}, "ascending": "Ascending", "@ascending": {"description": "Ascending sort order option"}, "all": "All", "@all": {"description": "All items option"}, "messages": "messages", "@messages": {"description": "Plural form of message"}, "messageCount": "{count} {count, plural, =1{message} other{messages}}", "@messageCount": {"description": "Displays the count of messages with proper pluralization", "placeholders": {"count": {"type": "int", "description": "Number of messages"}}}, "deleteLabelTooltip": "Delete label and emails", "@deleteLabelTooltip": {"description": "Tooltip for delete label button"}, "refreshInbox": "Refresh inbox", "@refreshInbox": {"description": "Accessibility label for refresh button"}, "deleteSelectedEmails": "Delete selected emails", "@deleteSelectedEmails": {"description": "Accessibility label for delete button"}, "sortOrder": "Sort order", "@sortOrder": {"description": "Accessibility label for sort dropdown"}, "selectAll": "Select all", "@selectAll": {"description": "Accessibility label for select all checkbox"}, "labelDeletedSuccessfully": "Label and associated emails deleted successfully", "@labelDeletedSuccessfully": {"description": "Success message when label is deleted"}, "failedToDeleteLabel": "Failed to delete label", "@failedToDeleteLabel": {"description": "Error message when label deletion fails"}, "errorLoadingLabelEmails": "Error loading label emails", "@errorLoadingLabelEmails": {"description": "Error message when loading emails for a label fails"}, "errorDuringDeletion": "Error during deletion", "@errorDuringDeletion": {"description": "Generic error message during deletion"}, "noEmailsForLabel": "No emails for this label", "@noEmailsForLabel": {"description": "Message when a label has no emails"}, "unknownSender": "Unknown sender", "@unknownSender": {"description": "Placeholder for unknown email sender"}, "noSubject": "(No subject)", "@noSubject": {"description": "Placeholder for emails without subject"}, "from": "From", "@from": {"description": "Email from field label"}, "group": "Group", "@group": {"description": "Group label for dropdown"}, "sortBy": "Sort by", "@sortBy": {"description": "Sort by label for dropdown (used for grouping options)"}, "none": "None", "@none": {"description": "None option for grouping"}, "sender": "Sender", "@sender": {"description": "Sender option for grouping"}, "year": "Year", "@year": {"description": "Year option for grouping"}, "month": "Month", "@month": {"description": "Month option for grouping"}, "date": "Date", "@date": {"description": "Date option for sorting"}, "order": "Order", "@order": {"description": "Sort order label"}, "unsubscribe": "구독 취소", "@unsubscribe": {"description": "Unsubscribe button label"}, "massUnsubscribeScreenTitle": "Mass Unsubscribe", "@massUnsubscribeScreenTitle": {"description": "Mass unsubscribe screen title"}, "newslettersAndPromotions": "Newsletters & Promotions", "@newslettersAndPromotions": {"description": "Section title for newsletters and promotional emails"}, "detectingNewsletters": "Detecting newsletters...", "@detectingNewsletters": {"description": "Loading message when detecting newsletters"}, "noNewslettersFound": "No newsletters found", "@noNewslettersFound": {"description": "Message when no newsletters are detected"}, "unsubscribeSelected": "Unsubscribe from selected", "@unsubscribeSelected": {"description": "Button to unsubscribe from selected newsletters"}, "unsubscribeConfirmation": "Unsubscribe from {count} {count, plural, =1{newsletter} other{newsletters}}?", "@unsubscribeConfirmation": {"description": "Confirmation dialog for mass unsubscribe", "placeholders": {"count": {"type": "int", "description": "Number of newsletters to unsubscribe from"}}}, "unsubscribeWarning": "This will attempt to unsubscribe you from the selected newsletters. This action may take some time and cannot be undone.", "@unsubscribeWarning": {"description": "Warning message for mass unsubscribe"}, "unsubscribeInProgress": "Unsubscribing...", "@unsubscribeInProgress": {"description": "Message during unsubscribe process"}, "unsubscribeCompleted": "Unsubscribe completed", "@unsubscribeCompleted": {"description": "Success message after unsubscribe"}, "unsubscribePartialSuccess": "{successful} of {total} unsubscribe attempts successful", "@unsubscribePartialSuccess": {"description": "Message for partial success in mass unsubscribe", "placeholders": {"successful": {"type": "int", "description": "Number of successful unsubscribes"}, "total": {"type": "int", "description": "Total number of unsubscribe attempts"}}}, "emailsFromSender": "{count} {count, plural, =1{email} other{emails}} from this sender", "@emailsFromSender": {"description": "Shows number of emails from a specific sender", "placeholders": {"count": {"type": "int", "description": "Number of emails from sender"}}}, "lastReceived": "Last received: {date}", "@lastReceived": {"description": "Shows when the last email was received", "placeholders": {"date": {"type": "String", "description": "Date of last received email"}}}, "unsubscribeLinkFound": "Unsubscribe link found", "@unsubscribeLinkFound": {"description": "Indicates that an unsubscribe link was found in the email"}, "noUnsubscribeLink": "No unsubscribe link found", "@noUnsubscribeLink": {"description": "Indicates that no unsubscribe link was found in the email"}, "lastSyncTime": "Last sync: {date}", "@lastSyncTime": {"description": "Shows the date and time of the last synchronization", "placeholders": {"date": {"type": "String", "description": "Formatted date and time of last sync"}}}, "neverSynced": "Never synchronized", "@neverSynced": {"description": "Message when no synchronization has been performed yet"}, "syncTooFrequent": "Synchronization too frequent", "@syncTooFrequent": {"description": "Title for sync rate limit error dialog"}, "syncRateLimitMessage": "Please wait {minutes} minutes before performing another manual synchronization. This helps protect Gmail API limits and ensures optimal performance.", "@syncRateLimitMessage": {"description": "Message explaining sync rate limit", "placeholders": {"minutes": {"type": "int", "description": "Number of minutes to wait"}}}, "syncRateLimitShort": "Please wait {minutes} min before next sync", "@syncRateLimitShort": {"description": "Short message for sync rate limit", "placeholders": {"minutes": {"type": "int", "description": "Number of minutes to wait"}}}, "ok": "OK", "@ok": {"description": "OK button label"}, "countdownConfirmMessage": "You can confirm in {seconds} seconds...", "@countdownConfirmMessage": {"description": "Message shown in reset confirmation dialog with countdown before OK is enabled.", "placeholders": {"seconds": {"type": "int", "description": "Seconds remaining before confirmation is enabled"}}}, "almostDone": "Almost done...", "estimatedTimeHours": "Estimated time left: ~{hours}h {minutes}min", "estimatedTimeMinutes": "Estimated time left: ~{minutes}min", "estimatedTimeSeconds": "Estimated time left: ~{seconds}s", "@estimatedTimeHours": {"placeholders": {"hours": {"type": "int"}, "minutes": {"type": "int"}}}, "@estimatedTimeMinutes": {"placeholders": {"minutes": {"type": "int"}}}, "@estimatedTimeSeconds": {"placeholders": {"seconds": {"type": "int"}}}, "syncHistoryIdInvalid": "Your Gmail history is too old or has been reset. A full synchronization will be started.", "@syncHistoryIdInvalid": {"description": "Message shown when Gmail historyId is invalid and a full sync is triggered."}, "syncError": "Sync error: {error}", "@syncError": {"description": "Generic sync error message with error details.", "placeholders": {"error": {"type": "String"}}}, "syncingEmails": "Syncing {processed} / {total} emails...", "@syncingEmails": {"description": "Progression de la synchronisation", "placeholders": {"processed": {"type": "int"}, "total": {"type": "int"}}}, "noEmailsFound": "No emails found in this Gmail account.", "@noEmailsFound": {"description": "Message displayed when no emails are found during sync."}, "syncInProgress": "Manual synchronization in progress...", "@syncInProgress": {"description": "Message displayed when manual sync is in progress."}, "checkingForUpdates": "Checking for updates...", "@checkingForUpdates": {"description": "Message displayed when checking for email updates."}, "performingIncrementalSync": "Performing incremental sync...", "@performingIncrementalSync": {"description": "Message displayed when performing incremental sync."}, "initialSyncMessage": "Initial sync: this may take several minutes depending on the number of emails. Please wait...", "@initialSyncMessage": {"description": "Message displayed during initial sync."}, "emailsSynced": "{count} {count, plural, =1{email} other{emails}} synced!", "@emailsSynced": {"description": "Message displayed when sync is completed.", "placeholders": {"count": {"type": "int"}}}, "syncCompletedNotificationTitle": "Synchronization completed!", "@syncCompletedNotificationTitle": {"description": "Title for sync completion notification."}, "syncCompletedNotificationBody": "Your emails are now up to date.", "@syncCompletedNotificationBody": {"description": "Body text for sync completion notification."}, "autoSyncStartedTitle": "Auto sync started", "@autoSyncStartedTitle": {"description": "Title for auto sync started notification"}, "autoSyncStartedBody": "Synchronizing your emails in background...", "@autoSyncStartedBody": {"description": "Body text for auto sync started notification"}, "autoSyncCompletedTitle": "Auto sync completed", "@autoSyncCompletedTitle": {"description": "Title for auto sync completed notification"}, "autoSyncCompletedBody": "Auto synchronization completed successfully.", "@autoSyncCompletedBody": {"description": "Body text for auto sync completed notification"}, "autoSyncCompletedWithNewEmailsBody": "Auto sync completed. {count} new {count, plural, =1{email} other{emails}} found.", "@autoSyncCompletedWithNewEmailsBody": {"description": "Body text for auto sync completed notification with new emails count", "placeholders": {"count": {"type": "int", "description": "Number of new emails found"}}}, "dataModeOffline": "오프라인", "@dataModeOffline": {"description": "Offline data mode option"}, "dataModeOnline": "온라인", "@dataModeOnline": {"description": "Online data mode option"}, "dataModeOfflineDescription": "로컬 캐시 사용, 빠르지만 최신 정보가 아닐 수 있음", "@dataModeOfflineDescription": {"description": "Description for offline mode"}, "dataModeOnlineDescription": "Gmail API 직접 사용, 항상 최신 정보", "@dataModeOnlineDescription": {"description": "Description for online mode"}, "currentMode": "현재 모드", "@currentMode": {"description": "Shows the current data mode"}, "errorLoadingData": "Error loading data", "@errorLoadingData": {"description": "Error message shown when data loading fails"}, "checkInternetConnectionAndRetry": "Check your internet connection and retry", "@checkInternetConnectionAndRetry": {"description": "Prompt to check internet connection and retry"}, "retryLoadingLabels": "Retry loading labels", "noLabelsFound": "No labels found", "unsubscribeResults": "Unsubscribe Results", "successful": "✅ Successful: {count}", "failed": "❌ Failed: {count}", "manualConfirmationNote": "Some newsletters may require manual confirmation via email. Check your inbox for confirmation emails.", "nextSyncIn": "Next Sync In", "minutes": "minutes", "notAvailable": "N/A", "spamEmailSummary": "Spam email from {from}, subject: {subject}, date: {date}", "fromSummary": "From: {from}\n{date}", "label": "Label", "home": "Home", "@home": {"description": "Home screen title"}, "totalEmails": "Total Emails: {count}", "@totalEmails": {"description": "Label for total emails count on home screen", "placeholders": {"count": {"type": "int"}}}, "pleaseLogInFirst": "Please log in first", "@pleaseLogInFirst": {"description": "Message shown when user is not logged in"}, "show": "Show", "@show": {"description": "Label for pagination show count"}, "perPage": "per page", "@perPage": {"description": "Label for pagination per page"}, "previous": "Previous", "@previous": {"description": "Tooltip for previous page button"}, "next": "Next", "@next": {"description": "Tooltip for next page button"}, "dataModeSwitchTitle": "Switch Data Mode", "@dataModeSwitchTitle": {"description": "Title for the dialog when switching between online and offline modes"}, "guest": "Guest", "@guest": {"description": "Label for guest user when not signed in"}, "notSignedIn": "Not signed in", "@notSignedIn": {"description": "Subtitle for guest user when not signed in"}, "unknownUser": "Unknown user", "@unknownUser": {"description": "Label for unknown user if displayName is missing"}, "noCategoriesFound": "카테고리를 찾을 수 없습니다", "@noCategoriesFound": {"description": "Message when no Gmail categories are found"}, "autoLogout": "Auto Logout", "autoLogoutDescription": "Automatically log out after a period of inactivity", "autoLogoutNever": "Never", "autoLogout5min": "5 minutes", "autoLogout15min": "15 minutes", "autoLogout30min": "30 minutes", "autoLogout1hour": "1 hour", "autoLogout3hours": "3 hours", "autoLogout6hours": "6 hours", "autoLogout12hours": "12 hours", "autoLogout24hours": "24 hours", "autoLogout48hours": "48 hours", "autoRefresh": "자동 새로고침", "autoRefreshDescription": "온라인 모드에서 이메일을 자동으로 새로고침", "autoRefreshNever": "안함", "autoRefresh5min": "5분", "autoRefresh15min": "15분", "autoRefresh30min": "30분", "autoRefresh1hour": "1시간", "autoRefresh2hours": "2시간", "autoRefresh6hours": "6시간", "autoRefresh12hours": "12시간", "autoRefresh24hours": "24시간", "sentLabel": "<PERSON><PERSON>", "@sentLabel": {"description": "Sent folder label"}, "trashLabel": "Trash", "@trashLabel": {"description": "Trash folder label"}, "socialLabel": "Social", "@socialLabel": {"description": "Social category label"}, "notificationsLabel": "Notifications", "@notificationsLabel": {"description": "Notifications category label"}, "forumsLabel": "Forums", "@forumsLabel": {"description": "Forums category label"}, "promotionsLabel": "Promotions", "@promotionsLabel": {"description": "Promotions category label"}, "korean": "한국어", "@korean": {"description": "Korean language option"}, "switchToOnlineModePrompt": "온라인 모드로 전환하시겠습니까?", "switchToOfflineModePrompt": "오프라인 모드로 전환하시겠습니까?", "loadingProfile": "프로필 로딩 중...", "loadingEmails": "이메일 로딩 중...", "online": "온라인", "offline": "오프라인", "composeEmailNotImplemented": "이메일 작성 기능이 구현되지 않았습니다", "composeEmail": "이메일 작성", "appDescription": "간단하고 안전한 이메일 애플리케이션", "version": "버전", "syncFetchingEmails": "이메일 가져오는 중...", "syncProcessingEmails": "이메일 처리 중...", "syncSyncingLabels": "라벨 동기화 중...", "syncCompleted": "동기화 완료", "spamStatistics": "스팸 통계", "totalSpamEmails": "총 스팸 이메일", "totalSpamSize": "총 스팸 크기", "appName": "LavaMail", "emailCategories": "이메일 카테고리", "refreshCounts": "개수 새로고침", "emailInformation": "이메일 정보", "totalEmailsLabel": "총 이메일", "attachments": "첨부파일", "noAttachmentsDetected": "첨부파일이 감지되지 않음", "attachmentsDetected": "예 ({count}개 첨부파일이 {emails}개 이메일에)", "error": "오류", "primary": "기본", "social": "소셜", "promotions": "프로모션", "updates": "알림", "forums": "포럼", "primaryCategoryDetails": "기본 카테고리 세부사항이 곧 제공됩니다!", "socialCategoryDetails": "소셜 카테고리 세부사항이 곧 제공됩니다!", "promotionsCategoryDetails": "프로모션 카테고리 세부사항이 곧 제공됩니다!", "updatesCategoryDetails": "알림 카테고리 세부사항이 곧 제공됩니다!", "forumsCategoryDetails": "포럼 카테고리 세부사항이 곧 제공됩니다!", "accountDetails": "계정 세부사항이 곧 제공됩니다!", "emailsLoadedSuccessfully": "이메일이 성공적으로 로드되었습니다", "errorLoadingEmails": "이메일 로딩 오류", "emailSync": "이메일 동기화", "initializing": "초기화 중...", "account": "계정", "autoSync": "자동 동기화", "automaticallySyncEmails": "백그라운드에서 자동으로 이메일 동기화", "networkSettings": "네트워크 설정", "preferredNetworkType": "선호하는 네트워크 유형", "networkTypeAuto": "자동", "networkTypeAutoDescription": "최적의 연결을 자동으로 선택", "networkTypeWifi": "WiFi", "networkTypeMobile": "모바일 데이터", "currentlyUsing": "현재 사용 중", "refreshNetworkStatus": "네트워크 상태 새로고침", "enableNotifications": "알림 활성화", "receiveNotifications": "새 이메일 알림 받기", "security": "보안", "manualSyncTooltip": "수동 동기화", "syncing": "동기화 중...", "signOut": "로그아웃", "synchronizationFailed": "동기화 실패", "user": "사용자", "gmailUser": "Gmail 사용자", "signInToGmail": "Gmail에 로그인", "signInWithEmailAppPassword": "이메일과 앱 비밀번호로 로그인", "emailAddress": "이메일 주소", "enterValidEmail": "유효한 이메일 주소를 입력하세요", "appPasswordLabel": "16자리 앱 비밀번호", "enterAppPassword16": "16자리 앱 비밀번호를 입력하세요", "gmailAppPasswordInfo": "중요: Gmail 앱 비밀번호를 사용해야 합니다 (기본 Google 비밀번호가 아님).\nGoogle 계정 보안 설정에서 생성하세요.", "saveCredentialsCheckbox": "자동 로그인을 위해 자격 증명 저장", "credentialsStoredSecurely": "자격 증명이 이 기기에 안전하게 저장됩니다", "signInToGmx": "GMX에 로그인", "gmxLogin": "GMX 로그인", "savedCredentialsFound": "저장된 자격 증명을 찾았습니다", "connectWithSavedCredentials": "저장된 자격 증명으로 연결", "useDifferentCredentials": "다른 자격 증명 사용", "password": "비밀번호", "gmxPasswordHint": "GMX 비밀번호 또는 앱 전용 비밀번호", "pleaseEnterYourPassword": "비밀번호를 입력하세요", "advancedSettings": "고급 설정", "imapHost": "IMAP 호스트", "smtpHost": "SMTP 호스트", "smtpConfiguration": "SMTP 구성:", "port587Starttls": "587번 포트 (STARTTLS)", "recommended": "추천", "port465SslTls": "465번 포트 (SSL/TLS)", "required": "필수", "invalid": "잘못됨", "loginToGmx": "GMX에 로그인", "importantSetupInformation": "중요 설정 정보", "gmxEnableImapInstructions": "1. GMX 계정에서 IMAP/SMTP 접근을 활성화하세요:\n   • GMX 웹메일에 로그인\n   • 설정 → POP3/IMAP 이동\n   • IMAP 접근 활성화", "ifYouHave2faEnabled": "2단계 인증이 활성화된 경우:", "gmxAppPasswordInstructions": "• GMX 설정 → 보안에서 앱 전용 비밀번호 생성\n• 일반 비밀번호 대신 앱 전용 비밀번호 사용", "gmxDefaultSettings": "기본 설정:\n• IMAP: imap.gmx.com:993 (SSL)\n• SMTP: mail.gmx.com:587 (STARTTLS) 또는 :465 (SSL/TLS)", "credentialsSavedSuccessfully": "자격 증명이 성공적으로 저장됨", "errorSavingCredentials": "자격 증명 저장 오류", "noSavedCredentialsFound": "저장된 자격 증명이 없습니다", "loginFailed": "로그인 실패: {error}", "@loginFailed": {"description": "로그인 실패 시 오류 메시지", "placeholders": {"error": {"type": "String"}}}, "signInToIcloud": "iCloud에 로그인", "icloudLogin": "iCloud 로그인", "signInToYahoo": "Yahoo에 로그인", "yahooLogin": "Yahoo 로그인", "appPassword": "앱 비밀번호", "icloudAppPasswordHint": "iCloud 앱 비밀번호", "yahooAppPasswordHint": "Yahoo 앱 비밀번호", "pleaseEnterYourAppPassword": "앱 비밀번호를 입력하세요", "icloudAppPasswordLength": "iCloud 앱 비밀번호는 일반적으로 16자입니다", "yahooAppPasswordLength": "Yahoo 앱 비밀번호는 일반적으로 16자입니다", "showAdvancedSettings": "고급 설정 표시", "hideAdvancedSettings": "고급 설정 숨기기", "imapPort": "IMAP 포트", "smtpPort": "SMTP 포트", "useSSL": "SSL 사용", "loginToIcloud": "iCloud에 로그인", "loginToYahoo": "Yahoo에 로그인", "icloudImportantInfo": "중요: iCloud 앱 비밀번호를 사용해야 합니다 (기본 Apple ID 비밀번호가 아님).\nApple ID 보안 설정에서 생성하세요.\n\n기본 설정:\n• IMAP: imap.mail.me.com:993 (SSL)\n• SMTP: smtp.mail.me.com:587 (SSL)", "yahooImportantInfo": "중요: Yahoo 앱 비밀번호를 사용해야 합니다 (기본 Yahoo 비밀번호가 아님).\nYahoo 계정 보안 설정에서 생성하세요.\n\n기본 설정:\n• IMAP: imap.mail.yahoo.com:993 (SSL)\n• SMTP: smtp.mail.yahoo.com:465 (SSL)", "port": "포트"}