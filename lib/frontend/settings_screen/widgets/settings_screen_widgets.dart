import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../../../core/gmail/common_functions/common_functions.dart';
import '../../../utils/app_settings.dart';

// =======================================================
// = Function : AutoLogoutDropdown
// = Description : Dropdown widget for selecting auto logout timeout duration
// =======================================================
class AutoLogoutDropdown extends StatelessWidget {
  final int value;
  final List<int> options;
  final Map<int, String> labels;
  final ValueChanged<int?> onChanged;
  const AutoLogoutDropdown({
    super.key,
    required this.value,
    required this.options,
    required this.labels,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return DropdownButton<int>(
      value: value,
      isExpanded: true,
      items: options.map((v) => DropdownMenuItem(
        value: v,
        child: Text(labels[v] ?? '$v min'),
      )).toList(),
      onChanged: onChanged,
    );
  }
}

// =======================================================
// = Function : AutoLogoutSection
// = Description : Section container for auto logout settings with title and description
// =======================================================
class AutoLogoutSection extends StatelessWidget {
  final String title;
  final String description;
  final Widget selector;
  const AutoLogoutSection({
    super.key,
    required this.title,
    required this.description,
    required this.selector,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: Theme.of(context).textTheme.titleMedium),
        const SizedBox(height: 4),
        Text(
          description,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
        ),
        const SizedBox(height: 8),
        selector,
      ],
    );
  }
}

// =======================================================
// = Function : ClearCacheButton
// = Description : Button widget for clearing cache with confirmation dialog
// =======================================================
class ClearCacheButton extends StatelessWidget {
  final String label;
  final bool isProcessing;
  final VoidCallback onConfirm;
  final String dialogTitle;
  final String dialogContent;
  final String cancelText;
  final String okText;
  const ClearCacheButton({
    super.key,
    required this.label,
    required this.isProcessing,
    required this.onConfirm,
    required this.dialogTitle,
    required this.dialogContent,
    required this.cancelText,
    required this.okText,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      icon: const Icon(Icons.delete_forever),
      label: Text(label),
      onPressed: isProcessing
          ? null
          : () {
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: Text(dialogTitle),
                  content: Text(dialogContent),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: Text(cancelText),
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                        onConfirm();
                      },
                      child: Text(okText),
                    ),
                  ],
                ),
              );
            },
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.redAccent,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 16),
      ),
    );
  }
}

// =======================================================
// = Function : DataModeSection
// = Description : Section container for data mode settings with bordered layout
// =======================================================
class DataModeSection extends StatelessWidget {
  final String title;
  final String currentModeLabel;
  final List<Widget> modeSelectors;
  const DataModeSection({
    super.key,
    required this.title,
    required this.currentModeLabel,
    required this.modeSelectors,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: Theme.of(context).textTheme.titleLarge),
        const SizedBox(height: 8),
        Text(
          currentModeLabel,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
        ),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(children: modeSelectors),
        ),
      ],
    );
  }
}

// =======================================================
// = Function : DividerSection
// = Description : Simple divider widget with customizable height
// =======================================================
class DividerSection extends StatelessWidget {
  final double height;
  const DividerSection({super.key, this.height = 32});

  @override
  Widget build(BuildContext context) {
    return Divider(height: height);
  }
}

// =======================================================
// = Function : LanguageSelector
// = Description : Placeholder widget for language selection functionality
// =======================================================
class LanguageSelector extends StatelessWidget {
  const LanguageSelector({super.key});

  @override
  Widget build(BuildContext context) {
    // TODO: Implement language selection logic
    return Text('Language selector not implemented');
  }
}

// =======================================================
// = Function : ManualSyncButton
// = Description : Button widget for manual sync with enabled/disabled states
// =======================================================
class ManualSyncButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final bool enabled;
  final String label;
  const ManualSyncButton({super.key, required this.onPressed, required this.enabled, required this.label});

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      icon: const Icon(Icons.sync),
      label: Text(label),
      onPressed: enabled ? onPressed : null,
      style: ElevatedButton.styleFrom(
        backgroundColor: enabled ? Colors.blueAccent : Colors.grey,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 16),
      ),
    );
  }
}

// =======================================================
// = Function : NetworkTypeSelector
// = Description : Dropdown widget for selecting network type (WiFi only or WiFi/Cellular)
// =======================================================
class NetworkTypeSelector extends StatelessWidget {
  final NetworkType value;
  final ValueChanged<NetworkType?> onChanged;
  final bool isProcessing;

  const NetworkTypeSelector({
    super.key,
    required this.value,
    required this.onChanged,
    this.isProcessing = false,
  });

  @override
  Widget build(BuildContext context) {
    return DropdownButton<NetworkType>(
      value: value,
      isExpanded: true,
      items: [
        DropdownMenuItem(
          value: NetworkType.wifi,
          child: const Text('Wi-Fi only'),
        ),
        DropdownMenuItem(
          value: NetworkType.mobile,
          child: const Text('Mobile data'),
        ),
      ],
      onChanged: isProcessing ? null : onChanged,
    );
  }
}

// =======================================================
// = Function : NotificationsSwitch
// = Description : Switch widget for enabling/disabling notifications
// =======================================================
class NotificationsSwitch extends StatelessWidget {
  final bool value;
  final ValueChanged<bool> onChanged;
  const NotificationsSwitch({super.key, required this.value, required this.onChanged});

  @override
  Widget build(BuildContext context) {
    return Switch(
      value: value,
      onChanged: onChanged,
    );
  }
}

// =======================================================
// = Function : SettingsProgressIndicator
// = Description : Progress indicator widget that shows only when processing
// =======================================================
class SettingsProgressIndicator extends StatelessWidget {
  final bool isProcessing;
  const SettingsProgressIndicator({super.key, required this.isProcessing});

  @override
  Widget build(BuildContext context) {
    if (!isProcessing) return const SizedBox.shrink();
    return const Center(child: CircularProgressIndicator());
  }
}

// =======================================================
// = Function : SettingsStatusText
// = Description : Status text widget that displays messages when available
// =======================================================
class SettingsStatusText extends StatelessWidget {
  final String? status;
  const SettingsStatusText({super.key, this.status});

  @override
  Widget build(BuildContext context) {
    if (status == null) return const SizedBox.shrink();
    return Center(
      child: Text(
        status!,
        style: const TextStyle(fontSize: 16),
        textAlign: TextAlign.center,
      ),
    );
  }
}

// =======================================================
// = Function : SyncFrequencyDropdown
// = Description : Dropdown widget for selecting sync frequency intervals
// =======================================================
class SyncFrequencyDropdown extends StatelessWidget {
  final int value;
  final List<int> options;
  final Map<int, String> labels;
  final ValueChanged<int?> onChanged;
  const SyncFrequencyDropdown({
    super.key,
    required this.value,
    required this.options,
    required this.labels,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return DropdownButton<int>(
      value: value,
      items: options.map((int v) => DropdownMenuItem<int>(
        value: v,
        child: Text(labels[v] ?? '$v min'),
      )).toList(),
      onChanged: onChanged,
    );
  }
}

// =======================================================
// = Function : SyncModeDropdown
// = Description : Dropdown widget for selecting between auto and manual sync modes
// =======================================================
class SyncModeDropdown extends StatelessWidget {
  final bool value;
  final ValueChanged<bool?> onChanged;
  final String autoLabel;
  final String manualLabel;
  const SyncModeDropdown({
    super.key,
    required this.value,
    required this.onChanged,
    required this.autoLabel,
    required this.manualLabel,
  });

  @override
  Widget build(BuildContext context) {
    return DropdownButton<bool>(
      value: value,
      items: [
        DropdownMenuItem<bool>(value: true, child: Text(autoLabel)),
        DropdownMenuItem<bool>(value: false, child: Text(manualLabel)),
      ],
      onChanged: onChanged,
    );
  }
}

// =======================================================
// = Function : SyncSection
// = Description : Main sync settings section that combines all sync-related widgets
// =======================================================
class SyncSection extends StatelessWidget {
  final String title;
  final Widget frequencySelector;
  final Widget networkTypeSelector;
  final Widget syncModeSelector;
  final Widget notificationsSwitch;
  final Widget? manualSyncBlock;
  const SyncSection({
    super.key,
    required this.title,
    required this.frequencySelector,
    required this.networkTypeSelector,
    required this.syncModeSelector,
    required this.notificationsSwitch,
    this.manualSyncBlock,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: Theme.of(context).textTheme.titleLarge),
        const SizedBox(height: 16),
        frequencySelector,
        const SizedBox(height: 16),
        networkTypeSelector,
        const SizedBox(height: 16),
        syncModeSelector,
        const SizedBox(height: 16),
        notificationsSwitch,
        if (manualSyncBlock != null) ...[
          const SizedBox(height: 24),
          manualSyncBlock!,
        ],
      ],
    );
  }
}

// =======================================================
// = Function : SyncStatusWidget
// = Description : Wrapper widget for displaying sync status information
// =======================================================
class SyncStatusWidget extends StatelessWidget {
  final Widget child;
  const SyncStatusWidget({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return child;
  }
}

// =======================================================
// = Function : NetworkTypeRadioGroup
// = Description : Widget for selecting network type using radio buttons
// =======================================================
class NetworkTypeRadioGroup extends StatelessWidget {
  final AppNetworkType selectedType;
  final ValueChanged<AppNetworkType> onChanged;

  const NetworkTypeRadioGroup({
    super.key,
    required this.selectedType,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return Column(
      children: [
        RadioListTile<AppNetworkType>(
          title: Text(l10n.networkTypeAuto),
          subtitle: Text(l10n.networkTypeAutoDescription),
          value: AppNetworkType.any,
          groupValue: selectedType,
          onChanged: (value) => onChanged(value!),
        ),
        RadioListTile<AppNetworkType>(
          title: Text(l10n.networkTypeWifi),
          value: AppNetworkType.wifi,
          groupValue: selectedType,
          onChanged: (value) => onChanged(value!),
        ),
        RadioListTile<AppNetworkType>(
          title: Text(l10n.networkTypeMobile),
          value: AppNetworkType.mobile,
          groupValue: selectedType,
          onChanged: (value) => onChanged(value!),
        ),
      ],
    );
  }
}