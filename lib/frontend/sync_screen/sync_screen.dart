import 'package:flutter/material.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:hive/hive.dart';
import 'package:percent_indicator/percent_indicator.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart' as gen_l10n;
import 'package:http/http.dart' as http;

import '../../core/gmail/online_mode/gmail_online_mode.dart' as online;
import '../../core/gmail/common_functions/common_functions.dart';
import '../home_screen/home_screen.dart';
import '../../utils/services/external/notification_service.dart';

// Global notifier for last sync time
final ValueNotifier<DateTime?> lastSyncNotifier = ValueNotifier<DateTime?>(null);

// =======================================================
// = Function : SyncScreen
// = Description : Screen widget that handles Gmail synchronization with progress tracking and error handling
// =======================================================
class SyncScreen extends StatefulWidget {
  final GoogleSignInAccount user;
  final VoidCallback? onDataChanged;

  const SyncScreen({super.key, required this.user, this.onDataChanged});

  @override
  State<SyncScreen> createState() => _SyncScreenState();
}

// =======================================================
// = Function : _SyncScreenState
// = Description : State management for sync screen with progress tracking, throttling and navigation
// =======================================================
class _SyncScreenState extends State<SyncScreen> {
  bool _isSyncing = false;
  String _status = '';
  SyncProgress? _syncProgress;
  double _progress = 0.0;

  // Simple throttling to avoid too many UI updates
  DateTime _lastUIUpdate = DateTime.now();
  static const Duration _uiUpdateThrottle = Duration(
    milliseconds: 200,
  ); // Max 5 updates per second

  @override
  void initState() {
    super.initState();
    // Initialize online service only
    // Initialize background sync service
    online.BackgroundSyncService.initialize(widget.user);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final l10n = gen_l10n.AppLocalizations.of(context);
      setState(() {
        _status = l10n.checkingDatabase;
      });
      _startSync();
    });
  }



  String _getStatusText(gen_l10n.AppLocalizations l10n, SyncProgress progress) {
    switch (progress.phase) {
      case SyncPhase.fetchingEmails:
        return l10n.syncFetchingEmails;
      case SyncPhase.processingEmails:
        return l10n.syncProcessingEmails;
      case SyncPhase.syncingLabels:
        return l10n.syncSyncingLabels;
      case SyncPhase.completed:
        return l10n.syncCompleted;
      default:
        return l10n.syncInProgress;
    }
  }

  // =======================================================
  // = Function : _showSyncCompletedNotification
  // = Description : Shows notification when sync is completed successfully
  // =======================================================
  Future<void> _showSyncCompletedNotification() async {
    final l10n = gen_l10n.AppLocalizations.of(context);
    await NotificationService.showSyncCompletedNotification(
      userEmail: widget.user.email,
      localizedTitle: l10n.syncCompletedNotificationTitle,
      localizedBody: l10n.syncCompletedNotificationBody,
    );
  }

  // =======================================================
  // = Function : _startSync
  // = Description : Main sync function that handles authentication, database checks and sync process
  // =======================================================
  Future<void> _startSync() async {
    if (!mounted) return;
    final l10n = gen_l10n.AppLocalizations.of(context);
    setState(() {
      _isSyncing = true;
      _syncProgress = null;
      _status = l10n.syncInProgress;
    });

    try {
      debugPrint('[SYNC] Starting synchronization for ${widget.user.email}');

      // Check authentication
      final auth = await widget.user.authentication;
      debugPrint('[SYNC] Access token obtained: ${auth.accessToken != null}');

      if (auth.accessToken == null) {
        throw Exception('No access token available');
      }

      // Validate token
      debugPrint('[SYNC] Validating token...');
      final response = await http.get(
        Uri.parse(
          'https://www.googleapis.com/oauth2/v1/tokeninfo?access_token=${auth.accessToken}',
        ),
      );

      if (response.statusCode != 200) {
        throw Exception('Token validation failed');
      }
      debugPrint('[SYNC] Token validated successfully');

      // Online mode only - no local database check needed
      debugPrint('[SYNC] Running in online mode only');

      if (mounted) {
        final l10n = gen_l10n.AppLocalizations.of(context);
        setState(() {
          // Show progress from the beginning, even at 0%
          _syncProgress = SyncProgress(
            phase: SyncPhase.fetchingEmails,
            currentItem: 0,
            totalItems: 0,
            progress: 0.0,
            startTime: DateTime.now(),
            estimatedEndTime: null,
            remainingTime: Duration.zero,
            message: null,
          );
          _progress = 0.0;
          _status = l10n.initialSyncMessage;
        });
      }

      // Online mode - no full sync needed
      debugPrint('[SYNC] Online mode - skipping full sync');

      // Save the last sync time
      debugPrint('[SYNC] Saving synchronization time...');
      final settingsBoxName =
          'sync_settings_${widget.user.email.replaceAll(RegExp(r'[^a-zA-Z0-9]'), '_')}';
      final settingsBox = await Hive.openBox(settingsBoxName);
      await settingsBox.put('last_sync_time', DateTime.now().toIso8601String());
      lastSyncNotifier.value = DateTime.now();

      // Show a summary before navigation
      setState(() {
        _isSyncing = false;
        final l10n = gen_l10n.AppLocalizations.of(context);
        if ((_syncProgress?.totalItems ?? 0) == 0) {
          _status = l10n.noEmailsFound;
        } else {
          _status = l10n.emailsSynced(_syncProgress?.processedEmails ?? _syncProgress?.currentItem ?? 0);
        }
        _progress = 1.0;
      });

      debugPrint('[SYNC] Synchronization completed successfully');

      try {
        await _showSyncCompletedNotification();
      } catch (_) {
        // Ignore any notification errors
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              gen_l10n.AppLocalizations.of(context).synchronizationCompleted,
            ),
            backgroundColor: Colors.green,
          ),
        );
        await Future.delayed(const Duration(seconds: 2));
        if (mounted) {
          if (widget.onDataChanged != null) {
            widget.onDataChanged!();
          }
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder: (context) => const HomeScreen(),
            ),
          );
        }
      }
    } catch (e, stack) {
      debugPrint('[SYNC][ERROR] $e\n$stack');
      setState(() {
        _isSyncing = false;

        final l10n = gen_l10n.AppLocalizations.of(context);
        // Handle rate limit exception specifically
        if (e.toString().contains('SyncRateLimitException')) {
          final match = RegExp(r'wait (\\d+) minutes').firstMatch(e.toString());
          final minutes =
              match != null ? int.tryParse(match.group(1)!) ?? 5 : 5;
          _status = l10n.syncRateLimitShort(minutes);
        } else if (e.toString().contains('history') &&
            e.toString().contains('400')) {
          // Gmail history error, full sync restarted
          _status = l10n.syncHistoryIdInvalid;
        } else {
          _status = l10n.syncError(e.toString());
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = gen_l10n.AppLocalizations.of(context);
    return Scaffold(
      appBar: AppBar(title: Text(l10n.sync)),
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Cercle de progression
              CircularPercentIndicator(
                radius: 70.0,
                lineWidth: 10.0,
                percent: _progress,
                center: Text(
                  '${(_progress * 100).toInt()}%',
                  style: const TextStyle(
                    fontSize: 24.0,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                progressColor: Theme.of(context).colorScheme.primary,
                backgroundColor: Theme.of(context).colorScheme.surfaceContainer,
                animation: false, // Disable animation to reduce CPU usage
                animateFromLastPercent: false,
              ),
              const SizedBox(height: 30),
              // Status text
              Text(
                _status,
                style: Theme.of(context).textTheme.titleMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              // Processed emails / total
              if (_syncProgress != null && _syncProgress!.totalItems > 0)
                Text(
                  '${_syncProgress!.processedEmails ?? _syncProgress!.currentItem} / ${_syncProgress!.totalEmails ?? _syncProgress!.totalItems} emails',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              // Display offline progress if applicable
              if (_syncProgress != null &&
                  _syncProgress!.phase == SyncPhase.processingOffline &&
                  _syncProgress!.offlineEmailsCount != null &&
                  _syncProgress!.offlineProcessedCount != null)
                Text(
                  '${_syncProgress!.offlineProcessedCount} / ${_syncProgress!.offlineEmailsCount} ${gen_l10n.AppLocalizations.of(context).offlineProcessing}',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
              const SizedBox(height: 20),
              // Temps restant
              if (_syncProgress != null)
                Builder(
                  builder: (context) {
                    final l10n = gen_l10n.AppLocalizations.of(context);
                    final remaining = _syncProgress!.remainingTime;
                    String text;
                    if (remaining == null) {
                      text = l10n.calculatingTime;
                    } else if (remaining.inSeconds <= 2) {
                      text = l10n.almostDone;
                    } else if (remaining.inHours > 0) {
                      text = l10n.estimatedTimeHours(
                        remaining.inHours,
                        remaining.inMinutes % 60,
                      );
                    } else if (remaining.inMinutes > 0) {
                      text = l10n.estimatedTimeMinutes(remaining.inMinutes);
                    } else {
                      text = l10n.estimatedTimeSeconds(remaining.inSeconds);
                    }
                    return Text(
                      text,
                      style: Theme.of(context).textTheme.bodyMedium,
                      textAlign: TextAlign.center,
                    );
                  },
                ),
              const SizedBox(height: 20), // Linear progress bar if no emails
              if (_isSyncing &&
                  (_syncProgress == null || _syncProgress!.totalItems == 0))
                const LinearProgressIndicator(),
              // Retry button if error
              if (!_isSyncing && _status.startsWith('Sync error'))
                ElevatedButton(onPressed: _startSync, child: Text(l10n.retry)),
            ],
          ),
        ),
      ),
    );
  }
}
