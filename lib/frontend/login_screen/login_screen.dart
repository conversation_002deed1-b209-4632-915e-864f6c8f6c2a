import 'package:flutter/material.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'dart:async'; // Add this import for TimeoutException
import '../../core/gmail/online_mode/gmail_online_mode.dart';
import '../home_screen/home_screen.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:logger/logger.dart';
import 'package:lavamail/frontend/theme/app_theme.dart';
import 'widgets/login_screen_widgets.dart';
import 'package:provider/provider.dart';
import 'package:lavamail/providers/user_provider.dart';
import 'package:lavamail/utils/helpers/validation/validation_helpers.dart';
import 'widgets/gmail_login_modal.dart';
import 'widgets/gmx_login_modal.dart';
import 'widgets/yahoo_login_modal.dart';
import 'widgets/icloud_login_modal.dart';

// Constants for animation and timing
class LoginScreenConstants {
  static const Duration animationDuration = Duration(milliseconds: 1200);
  static const Duration navigationTimeout = Duration(seconds: 30); // Augmenté pour le chargement en arrière-plan
  static const String appVersion = '1.0.0';
}

// =======================================================
// = Function : LoginScreen
// = Description : Main login screen that handles Google authentication and user sign-in flow
// =======================================================
class LoginScreen extends StatefulWidget {
  /// Callback function when sign in is successful
  final Function(GoogleSignInAccount)? onSignIn;
  
  /// Authentication service for Google sign in
  final GmailAuthService? authService;

  const LoginScreen({super.key, this.onSignIn, this.authService});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

// =======================================================
// = Function : _LoginScreenState
// = Description : State management for login screen with animation controller and authentication logic
// =======================================================
class _LoginScreenState extends State<LoginScreen> with SingleTickerProviderStateMixin {
  bool _isLoading = false;
  String? _errorMessage;
  late final GmailAuthService _googleAuth;
  late AnimationController _animationController;
  late Animation<double> _fadeInAnimation;
  final _logger = Logger();

  @override
  void initState() {
    super.initState();
    _initializeAuthService();
    _initializeAnimation();
  }

  void _initializeAuthService() {
    _googleAuth = widget.authService ?? GmailAuthService();
  }

  void _initializeAnimation() {
    _animationController = AnimationController(
      vsync: this,
      duration: LoginScreenConstants.animationDuration,
    );
    _fadeInAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeIn,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }



  // =======================================================
  // = Function : _handleGoogleSignIn
  // = Description : Handles Google sign-in process with error handling and navigation
  // =======================================================
  Future<void> _handleGoogleSignIn() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final userCredential = await _googleAuth.signInWithGoogle();
      if (!mounted) return;

      if (userCredential != null) {
        await _handleSuccessfulSignIn(userCredential);
      } else {
        _handleSignInCancellation();
      }
    } catch (e) {
      _handleSignInError(e);
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _handleSuccessfulSignIn(GoogleSignInAccount userCredential) async {
    try {
      // Update global provider
      Provider.of<UserProvider>(context, listen: false).setUser(userCredential);
      
      // Call the onSignIn callback if defined
      if (widget.onSignIn != null) {
        widget.onSignIn!(userCredential);
      } else {
        await _navigateToHomeScreen();
      }
    } catch (e, stack) {
      _logger.e('Error during successful sign in', error: e, stackTrace: stack);
      if (mounted) {
        setState(() {
          _errorMessage = AppLocalizations.of(context).signInFailed;
        });
      }
    }
  }

  void _handleSignInCancellation() {
    if (mounted) {
      setState(() {
        _errorMessage = AppLocalizations.of(context).signInCancelled;
      });
    }
  }

  void _handleSignInError(dynamic error) {
    if (!mounted) return;

    _logger.e('Sign in error', error: error);

    setState(() {
      String errorMessage = error.toString();

      // Provide more specific error messages for common issues
      if (errorMessage.contains('PlatformException')) {
        if (errorMessage.contains('sign_in_failed')) {
          errorMessage = '${AppLocalizations.of(context).signInFailed} Please check your internet connection.';
        } else if (errorMessage.contains('network_error')) {
          errorMessage = 'Network error. Please check your internet connection and try again.';
        } else if (errorMessage.contains('sign_in_canceled')) {
          errorMessage = AppLocalizations.of(context).signInCancelled;
        } else {
          errorMessage = 'Authentication error. Please try again or check your internet connection.';
        }
      } else if (errorMessage.contains('No internet connection')) {
        errorMessage = 'No internet connection. Please check your network and try again.';
      } else if (errorMessage.contains('Network error')) {
        errorMessage = 'Network error. Please check your internet connection and try again.';
      } else if (errorMessage.contains('Failed to obtain authentication tokens')) {
        errorMessage = 'Authentication failed. Please try signing in again.';
      } else {
        errorMessage = ErrorMessageHelper.sanitizeErrorMessage(
          errorMessage,
          AppLocalizations.of(context),
        );
      }

      _errorMessage = errorMessage;
    });
  }

  Future<void> _navigateToHomeScreen() async {
    try {
      await Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => const HomeScreen(),
        ),
      ).timeout(
        LoginScreenConstants.navigationTimeout,
        onTimeout: () {
          throw TimeoutException('Navigation timeout');
        },
      );
    } catch (e, stack) {
      _logger.e('Navigation error', error: e, stackTrace: stack);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).signInFailed),
            backgroundColor: LavaMailTheme.errorColor,
          ),
        );
      }
    }
  }

  void _showGmailLoginModal() {
    // Set provider type immediately when user selects Gmail
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    userProvider.userType = UserType.gmail;
    _logger.i('Provider type set to Gmail');

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => GmailLoginModal(
        onGoogleSignIn: () {
          Navigator.of(context).pop();
          _handleGoogleSignIn();
        },
        onAppPasswordSignIn: (email, appPassword) async {
          setState(() { _isLoading = true; _errorMessage = null; });
          try {
            // Store context reference before async operation
            final userProvider = Provider.of<UserProvider>(context, listen: false);

            await _googleAuth.signInWithAppPassword(email, appPassword);
            if (!mounted) return;

            // On success, update provider and navigate to home
            userProvider.setImapUser(ImapUser(email));
            userProvider.userType = UserType.gmail;
            setState(() { _isLoading = false; });
            await _navigateToHomeScreen();
          } catch (e) {
            if (mounted) {
              setState(() { _isLoading = false; _errorMessage = e.toString(); });
            }
            rethrow;
          }
        },
      ),
    ).then((_) {
      // Reset provider type if user goes back without logging in
      if (mounted) {
        final currentUserProvider = Provider.of<UserProvider>(context, listen: false);
        if (currentUserProvider.user == null) {
          currentUserProvider.userType = UserType.gmail; // Keep Gmail as default
        }
      }
    });
  }

  void _showGmxLoginModal() {
    // Set provider type immediately when user selects GMX
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    userProvider.userType = UserType.gmx;
    _logger.i('Provider type set to GMX');

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => GmxLoginModal(),
    ).then((_) {
      // Reset provider type if user goes back without logging in
      if (mounted) {
        final currentUserProvider = Provider.of<UserProvider>(context, listen: false);
        if (currentUserProvider.gmxUser == null) {
          currentUserProvider.userType = UserType.gmail; // Reset to default
        }
      }
    });
  }

  void _showYahooLoginModal() {
    // Set provider type immediately when user selects Yahoo
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    userProvider.userType = UserType.yahoo;
    _logger.i('Provider type set to Yahoo');

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => YahooLoginModal(),
    ).then((_) {
      // Reset provider type if user goes back without logging in
      if (mounted) {
        final currentUserProvider = Provider.of<UserProvider>(context, listen: false);
        if (currentUserProvider.yahooUser == null) {
          currentUserProvider.userType = UserType.gmail; // Reset to default
        }
      }
    });
  }

  void _showIcloudLoginModal() {
    // Set provider type immediately when user selects iCloud
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    userProvider.userType = UserType.icloud;
    _logger.i('Provider type set to iCloud');

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => IcloudLoginModal(),
    ).then((_) {
      // Reset provider type if user goes back without logging in
      if (mounted) {
        final currentUserProvider = Provider.of<UserProvider>(context, listen: false);
        if (currentUserProvider.icloudUser == null) {
          currentUserProvider.userType = UserType.gmail; // Reset to default
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              LavaMailTheme.backgroundColor,
              Colors.white,
              LavaMailTheme.backgroundColor.withValues(alpha: 0.8),
            ],
            stops: const [0.0, 0.5, 1.0],
          ),
        ),
        child: SafeArea(
          child: LoginFadeTransition(
            animation: _fadeInAnimation,
            child: Center(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: LavaMailTheme.spacingXL),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const AppLogo(),
                    LoginProviderSelector(
                      onGmail: _showGmailLoginModal,
                      onYahoo: _showYahooLoginModal,
                      onGmx: _showGmxLoginModal,
                      onIcloud: _showIcloudLoginModal,
                      isLoadingGmail: _isLoading,
                      isLoadingYahoo: false,
                      isLoadingGmx: false,
                      isLoadingIcloud: false,
                    ),
                    if (_errorMessage != null) ...[
                      const SizedBox(height: 24),
                      LoginErrorMessage(message: _errorMessage!),
                    ],
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
