/*
=======================================================
= File: icloud_login_modal.dart
= Project: LavaMail
= Description:
=   - iCloud login modal for email authentication
=   - Offers a selector and then the iCloud login form
=   - All code, comments, and documentation are in English as per project standards.
=======================================================
*/

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:logger/logger.dart';
import '../../../core/icloud/auth/icloud_auth_service.dart';
import '../../../providers/user_provider.dart';
import '../../home_screen/home_screen.dart';
import '../../theme/app_theme.dart';
import '../../theme/widgets/common_widgets.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class IcloudLoginModal extends StatefulWidget {
  final VoidCallback? onBack;

  const IcloudLoginModal({super.key, this.onBack});

  @override
  State<IcloudLoginModal> createState() => _IcloudLoginModalState();
}

class _IcloudLoginModalState extends State<IcloudLoginModal> {
  @override
  Widget build(BuildContext context) {
    return LavaMailModal(
      title: AppLocalizations.of(context).signInToIcloud,
      titleIcon: Icons.cloud,
      content: IcloudLoginForm(
        onBack: () {
          if (widget.onBack != null) {
            widget.onBack!();
          } else {
            Navigator.of(context).pop();
          }
        },
      ),
      actions: [
        LavaMailOutlinedButton(
          text: AppLocalizations.of(context).cancel,
          onPressed: () {
            if (widget.onBack != null) {
              widget.onBack!();
            } else {
              Navigator.of(context).pop();
            }
          },
        ),
      ],
    );
  }
}

class IcloudLoginForm extends StatefulWidget {
  final VoidCallback onBack;
  const IcloudLoginForm({super.key, required this.onBack});

  @override
  State<IcloudLoginForm> createState() => _IcloudLoginFormState();
}

class _IcloudLoginFormState extends State<IcloudLoginForm> {
  final Logger _logger = Logger();
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _appPasswordController = TextEditingController();
  final _imapHostController = TextEditingController(text: 'imap.mail.me.com');
  final _imapPortController = TextEditingController(text: '993');
  final _smtpHostController = TextEditingController(text: 'smtp.mail.me.com');
  final _smtpPortController = TextEditingController(text: '587');
  bool _isLoading = false;
  bool _useSSL = true;
  bool _showAdvancedSettings = false;
  bool _obscurePassword = true;
  bool _hasCredentialsSaved = false;
  bool _showDirectConnect = false;
  bool _saveCredentials = false; // Checkbox state for saving credentials

  @override
  void initState() {
    super.initState();
    _loadSavedCredentials();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _appPasswordController.dispose();
    _imapHostController.dispose();
    _imapPortController.dispose();
    _smtpHostController.dispose();
    _smtpPortController.dispose();
    super.dispose();
  }

  /// Loads saved iCloud credentials and pre-fills the form
  Future<void> _loadSavedCredentials() async {
    try {
      final savedUser = await IcloudAuthService.loadSavedUser();
      if (savedUser != null && mounted) {
        setState(() {
          _hasCredentialsSaved = true;
          _showDirectConnect = true;
          // Pre-fill email (visible) and password (hidden)
          _emailController.text = savedUser.email;
          _appPasswordController.text = savedUser.appPassword;
          _imapHostController.text = savedUser.imapHost;
          _imapPortController.text = savedUser.imapPort.toString();
          _smtpHostController.text = savedUser.smtpHost;
          _smtpPortController.text = savedUser.smtpPort.toString();
          _useSSL = savedUser.useSSL;
        });
        _logger.i('iCloud credentials loaded and form pre-filled for: ${savedUser.email}');
      }
    } catch (e) {
      _logger.w('Failed to load saved iCloud credentials: $e');
    }
  }

  /// Saves user credentials securely if user chose to save them
  Future<void> _saveUserCredentials(dynamic user) async {
    try {
      if (_saveCredentials && user != null) {
        await user.saveCredentials();
        _logger.i('iCloud credentials saved for: ${user.email}');

        // Show confirmation to user
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.of(context).credentialsSavedSuccessfully),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      _logger.e('Failed to save iCloud credentials: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).errorSavingCredentials),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 3),
          ),
        );
      }
    }
  }

  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }
    setState(() { _isLoading = true; });
    try {
      _logger.d('Attempting iCloud login');
      final user = await IcloudAuthService.signInWithCredentials(
        email: _emailController.text.trim(),
        appPassword: _appPasswordController.text,
        imapHost: _imapHostController.text.trim(),
        imapPort: int.parse(_imapPortController.text),
        smtpHost: _smtpHostController.text.trim(),
        smtpPort: int.parse(_smtpPortController.text),
        useSSL: _useSSL,
      );
      if (user != null) {
        // Save credentials if user chose to
        await _saveUserCredentials(user);

        if (mounted) {
          final userProvider = Provider.of<UserProvider>(context, listen: false);
          userProvider.userType = UserType.icloud;
          userProvider.setIcloudUser(user);

          // Start data loading process as per Mermaid diagram
          await _startDataLoading(user);

          if (mounted) {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(builder: (context) => const HomeScreen()),
            );
          }
        }
      }
    } catch (e) {
      _logger.e('iCloud login failed', error: e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).loginFailed(e.toString())),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() { _isLoading = false; });
      }
    }
  }

  /// Handles direct connection with saved credentials
  Future<void> _handleDirectConnect() async {
    if (_emailController.text.isEmpty || _appPasswordController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context).noSavedCredentialsFound),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }
    await _handleLogin();
  }

  /// Starts the data loading process as defined in the Mermaid diagram
  Future<void> _startDataLoading(dynamic user) async {
    try {
      _logger.i('Starting iCloud data loading process...');

      // Step 1: Load metadata (placeholder for now)
      _logger.d('Loading iCloud metadata...');

      // Step 2: Load folders/labels (placeholder for now)
      _logger.d('Loading iCloud folders/labels...');

      // Step 3: Count attachments (placeholder for now)
      _logger.d('Counting iCloud attachments...');

      _logger.i('iCloud data loading completed');
    } catch (e) {
      _logger.w('Error during iCloud data loading: $e');
      // Continue to home screen even if data loading fails
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Row(
            children: [
              IconButton(
                onPressed: widget.onBack,
                icon: const Icon(Icons.arrow_back),
              ),
              const SizedBox(width: 8),
              const Icon(Icons.cloud, color: Colors.blue, size: 32),
              const SizedBox(width: 12),
              Text(
                AppLocalizations.of(context).icloudLogin,
                style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 24),
          // Show direct connect button if credentials are saved
          if (_showDirectConnect && _hasCredentialsSaved) ...[
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Icon(Icons.check_circle, color: Colors.blue.shade700, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        AppLocalizations.of(context).savedCredentialsFound,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '${AppLocalizations.of(context).emailAddress}: ${_emailController.text}',
                    style: const TextStyle(fontSize: 14),
                  ),
                  const SizedBox(height: 12),
                  ElevatedButton.icon(
                    onPressed: _isLoading ? null : _handleDirectConnect,
                    icon: const Icon(Icons.cloud),
                    label: Text(AppLocalizations.of(context).connectWithSavedCredentials),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      minimumSize: const Size(double.infinity, 45),
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextButton(
                    onPressed: () {
                      setState(() {
                        _showDirectConnect = false;
                      });
                    },
                    child: Text(AppLocalizations.of(context).useDifferentCredentials),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Show form if no direct connect or user chose different credentials
          if (!_showDirectConnect || !_hasCredentialsSaved)
          Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                TextFormField(
                  controller: _emailController,
                  decoration: InputDecoration(
                    labelText: AppLocalizations.of(context).emailAddress,
                    hintText: '<EMAIL>',
                    prefixIcon: const Icon(Icons.email_outlined),
                    border: const OutlineInputBorder(),
                    filled: _hasCredentialsSaved,
                    fillColor: _hasCredentialsSaved ? Colors.blue.shade50 : null,
                  ),
                  keyboardType: TextInputType.emailAddress,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return AppLocalizations.of(context).enterValidEmail;
                    }
                    if (!value.contains('@')) {
                      return AppLocalizations.of(context).enterValidEmail;
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _appPasswordController,
                  decoration: InputDecoration(
                    labelText: AppLocalizations.of(context).appPassword,
                    hintText: _hasCredentialsSaved ? '••••••••••••••••' : AppLocalizations.of(context).icloudAppPasswordHint,
                    prefixIcon: const Icon(Icons.lock_outlined),
                    suffixIcon: IconButton(
                      onPressed: () {
                        setState(() {
                          _obscurePassword = !_obscurePassword;
                        });
                      },
                      icon: Icon(_obscurePassword ? Icons.visibility : Icons.visibility_off),
                    ),
                    border: const OutlineInputBorder(),
                    filled: _hasCredentialsSaved,
                    fillColor: _hasCredentialsSaved ? Colors.blue.shade50 : null,
                  ),
                  obscureText: _obscurePassword,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return AppLocalizations.of(context).pleaseEnterYourAppPassword;
                    }
                    if (value.length < 16) {
                      return AppLocalizations.of(context).icloudAppPasswordLength;
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 8),
                TextButton(
                  onPressed: () {
                    setState(() {
                      _showAdvancedSettings = !_showAdvancedSettings;
                    });
                  },
                  child: Text(_showAdvancedSettings ? AppLocalizations.of(context).hideAdvancedSettings : AppLocalizations.of(context).showAdvancedSettings),
                ),
                if (_showAdvancedSettings) ...[
                  const SizedBox(height: 8),
                  TextFormField(
                    controller: _imapHostController,
                    decoration: InputDecoration(
                      labelText: AppLocalizations.of(context).imapHost,
                      border: const OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextFormField(
                    controller: _imapPortController,
                    decoration: InputDecoration(
                      labelText: AppLocalizations.of(context).imapPort,
                      border: const OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                  const SizedBox(height: 8),
                  TextFormField(
                    controller: _smtpHostController,
                    decoration: InputDecoration(
                      labelText: AppLocalizations.of(context).smtpHost,
                      border: const OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextFormField(
                    controller: _smtpPortController,
                    decoration: InputDecoration(
                      labelText: AppLocalizations.of(context).smtpPort,
                      border: const OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                  const SizedBox(height: 8),
                  SwitchListTile(
                    title: Text(AppLocalizations.of(context).useSSL),
                    value: _useSSL,
                    onChanged: (v) => setState(() => _useSSL = v),
                  ),
                ],
                const SizedBox(height: 16),

                // Checkbox for saving credentials
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Checkbox(
                      value: _saveCredentials,
                      onChanged: (value) {
                        setState(() {
                          _saveCredentials = value ?? false;
                        });
                      },
                      activeColor: LavaMailTheme.primaryColor,
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      visualDensity: VisualDensity.compact,
                    ),
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            _saveCredentials = !_saveCredentials;
                          });
                        },
                        child: Padding(
                          padding: const EdgeInsets.only(top: 12.0),
                          child: Text(
                            AppLocalizations.of(context).saveCredentialsCheckbox,
                            style: LavaMailTheme.bodyMedium,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),

                if (_saveCredentials) ...[
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(color: Colors.blue.shade200),
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Icon(
                          Icons.security,
                          size: 16,
                          color: Colors.blue.shade600,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            AppLocalizations.of(context).credentialsStoredSecurely,
                            style: LavaMailTheme.bodySmall.copyWith(
                              color: Colors.blue.shade700,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],

                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _isLoading ? null : _handleLogin,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : Text(
                          AppLocalizations.of(context).loginToIcloud,
                          style: const TextStyle(fontSize: 16),
                        ),
                ),
                const SizedBox(height: 16),
                Text(
                  AppLocalizations.of(context).icloudImportantInfo,
                  style: const TextStyle(fontSize: 11, color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
} 