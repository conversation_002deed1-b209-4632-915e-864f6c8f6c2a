import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';
import '../../theme/widgets/common_widgets.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class GmailLoginModal extends StatefulWidget {
  final VoidCallback onGoogleSignIn;
  final Future<void> Function(String email, String appPassword) onAppPasswordSignIn;

  const GmailLoginModal({
    super.key,
    required this.onGoogleSignIn,
    required this.onAppPasswordSignIn,
  });

  @override
  State<GmailLoginModal> createState() => _GmailLoginModalState();
}

class _GmailLoginModalState extends State<GmailLoginModal> {
  bool showAppPasswordForm = false;
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _appPasswordController = TextEditingController();
  String email = '';
  String appPassword = '';
  bool isLoading = false;
  String? errorMessage;
  final bool _hasCredentialsSaved = false;
  bool _obscurePassword = true;
  bool _saveCredentials = false; // Checkbox state for saving credentials

  @override
  void initState() {
    super.initState();
    _loadSavedCredentials();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _appPasswordController.dispose();
    super.dispose();
  }

  /// Loads saved Gmail credentials and pre-fills the form
  Future<void> _loadSavedCredentials() async {
    try {
      // For Gmail, we would need to check if there are saved app password credentials
      // This is a placeholder for now since Gmail primarily uses OAuth
      // but we can still check for app password credentials if they exist

      // TODO: Implement Gmail app password credential loading
      // For now, this is a placeholder

    } catch (e) {
      // Failed to load saved Gmail credentials
    }
  }

  void _submitAppPassword() async {
    if (!_formKey.currentState!.validate()) return;
    setState(() { isLoading = true; errorMessage = null; });
    try {
      await widget.onAppPasswordSignIn(_emailController.text.trim(), _appPasswordController.text);
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        setState(() { errorMessage = e.toString(); });
      }
    } finally {
      if (mounted) {
        setState(() { isLoading = false; });
      }
    }
  }



  @override
  Widget build(BuildContext context) {
    return LavaMailModal(
      title: AppLocalizations.of(context).signInToGmail,
      titleIcon: Icons.email,
      onBack: showAppPasswordForm ? (() => setState(() => showAppPasswordForm = false)) : null,
      content: showAppPasswordForm ? _buildLoginForm() : _buildProviderSelector(),
      actions: _buildActions(),
    );
  }

  Widget _buildProviderSelector() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        LavaMailButton(
          text: AppLocalizations.of(context).signInWithGmail,
          icon: Icons.login,
          onPressed: widget.onGoogleSignIn,
        ),
        const SizedBox(height: LavaMailTheme.spacingM),
        LavaMailButton(
          text: AppLocalizations.of(context).signInWithEmailAppPassword,
          icon: Icons.lock,
          onPressed: () => setState(() => showAppPasswordForm = true),
        ),
      ],
    );
  }

  Widget _buildLoginForm() {
    return SingleChildScrollView(
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            LavaMailTextFormField(
              label: AppLocalizations.of(context).emailAddress,
              controller: _emailController,
              keyboardType: TextInputType.emailAddress,
              filled: _hasCredentialsSaved,
              fillColor: _hasCredentialsSaved ? LavaMailTheme.primaryColor.withValues(alpha: 0.1) : null,
              onChanged: (v) => email = v,
              validator: (v) => v != null && v.contains('@') ? null : AppLocalizations.of(context).enterValidEmail,
            ),
            const SizedBox(height: LavaMailTheme.spacingM),
            LavaMailTextFormField(
              label: AppLocalizations.of(context).appPassword,
              hint: _hasCredentialsSaved ? '••••••••••••••••' : null,
              controller: _appPasswordController,
              obscureText: _obscurePassword,
              filled: _hasCredentialsSaved,
              fillColor: _hasCredentialsSaved ? LavaMailTheme.primaryColor.withValues(alpha: 0.1) : null,
              suffixIcon: IconButton(
                onPressed: () {
                  setState(() {
                    _obscurePassword = !_obscurePassword;
                  });
                },
                icon: Icon(_obscurePassword ? Icons.visibility : Icons.visibility_off),
              ),
              onChanged: (v) => appPassword = v,
              validator: (v) => v != null && v.length == 16 ? null : AppLocalizations.of(context).enterAppPassword16,
            ),
            const SizedBox(height: LavaMailTheme.spacingM),
            Container(
              padding: const EdgeInsets.all(LavaMailTheme.spacingM),
              decoration: BoxDecoration(
                color: LavaMailTheme.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(LavaMailTheme.borderRadiusS),
              ),
              child: Text(
                AppLocalizations.of(context).gmailAppPasswordInfo,
                style: LavaMailTheme.bodySmall,
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: LavaMailTheme.spacingM),

            // Checkbox for saving credentials
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Checkbox(
                  value: _saveCredentials,
                  onChanged: (value) {
                    setState(() {
                      _saveCredentials = value ?? false;
                    });
                  },
                  activeColor: LavaMailTheme.primaryColor,
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  visualDensity: VisualDensity.compact,
                ),
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _saveCredentials = !_saveCredentials;
                      });
                    },
                    child: Padding(
                      padding: const EdgeInsets.only(top: 12.0),
                      child: Text(
                        AppLocalizations.of(context).saveCredentialsCheckbox,
                        style: LavaMailTheme.bodyMedium,
                      ),
                    ),
                  ),
                ),
              ],
            ),

            if (_saveCredentials) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.security,
                      size: 16,
                      color: Colors.blue.shade600,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        AppLocalizations.of(context).credentialsStoredSecurely,
                        style: LavaMailTheme.bodySmall.copyWith(
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],

            if (errorMessage != null) ...[
              const SizedBox(height: LavaMailTheme.spacingM),
              LavaMailErrorMessage(message: errorMessage!),
            ],
          ],
        ),
      ),
    );
  }

  List<Widget> _buildActions() {
    if (!showAppPasswordForm) {
      return [
        LavaMailOutlinedButton(
          text: AppLocalizations.of(context).cancel,
          onPressed: () => Navigator.of(context).pop(),
        ),
      ];
    }

    return [
      LavaMailOutlinedButton(
        text: AppLocalizations.of(context).cancel,
        onPressed: () => Navigator.of(context).pop(),
      ),
      LavaMailButton(
        text: AppLocalizations.of(context).confirm,
        onPressed: _submitAppPassword,
        isLoading: isLoading,
      ),
    ];
  }
} 