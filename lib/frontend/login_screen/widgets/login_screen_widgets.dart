import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:lavamail/frontend/theme/app_theme.dart';
import 'package:lavamail/frontend/theme/widgets/common_widgets.dart';

// Constants for widget dimensions and styling
class LoginWidgetConstants {
  static const double logoSize = 120.0;
  static const double buttonHeight = 48.0;
  static const double buttonBorderRadius = 20.0;
  static const double googleIconSize = 20.0;
  static const double versionTextSize = 12.0;
  static const double errorTextSize = 14.0;
  static const double buttonTextSize = 16.0;
  static const double buttonHorizontalPadding = 12.0;
  static const double buttonIconSpacing = 12.0;
  static const double buttonStrokeWidth = 2.0;
}

// =======================================================
// = Function : AppLogo
// = Description : Displays the app logo with modern professional styling
// =======================================================
class AppLogo extends StatelessWidget {
  const AppLogo({super.key});

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final screenHeight = mediaQuery.size.height;
    final screenWidth = mediaQuery.size.width;

    return Column(
      children: [
        SizedBox(height: screenHeight * 0.08),
        // Logo container with modern styling
        Container(
          padding: const EdgeInsets.all(LavaMailTheme.spacingL),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: LavaMailTheme.primaryColor.withValues(alpha: 0.1),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Container(
            width: screenWidth * 0.25,
            height: screenWidth * 0.25,
            constraints: const BoxConstraints(
              minWidth: 80,
              maxWidth: 120,
              minHeight: 80,
              maxHeight: 120,
            ),
            child: Image.asset(
              'assets/images/logo.png',
              fit: BoxFit.contain,
            ),
          ),
        ),
        const SizedBox(height: LavaMailTheme.spacingL),
        // App title with gradient effect
        ShaderMask(
          shaderCallback: (bounds) => LinearGradient(
            colors: [
              LavaMailTheme.primaryColor,
              LavaMailTheme.primaryColor.withValues(alpha: 0.8),
            ],
          ).createShader(bounds),
          child: Text(
            'LAVAMAIL',
            textAlign: TextAlign.center,
            style: LavaMailTheme.headingLarge.copyWith(
              color: Colors.white,
            ),
          ),
        ),
        const SizedBox(height: LavaMailTheme.spacingS),
        Text(
          'Professional Email Management',
          textAlign: TextAlign.center,
          style: LavaMailTheme.bodyMedium.copyWith(
            color: LavaMailTheme.textSecondaryColor,
            letterSpacing: 0.5,
          ),
        ),
      ],
    );
  }
}

// =======================================================
// = Function : GoogleSignInButton
// = Description : Custom Google sign-in button with loading state and Google logo
// =======================================================
class GoogleSignInButton extends StatelessWidget {
  /// The text to display on the button
  final String text;
  
  /// Callback function when the button is pressed
  final VoidCallback onPressed;
  
  /// Whether the button is in loading state
  final bool isLoading;

  const GoogleSignInButton({
    super.key, 
    required this.text, 
    required this.onPressed, 
    this.isLoading = false
  });

  @override
  Widget build(BuildContext context) {
    return Semantics(
      button: true,
      enabled: !isLoading,
      label: text,
      child: SizedBox(
        width: double.infinity,
        height: LoginWidgetConstants.buttonHeight,
        child: OutlinedButton(
          onPressed: isLoading ? null : onPressed,
          style: OutlinedButton.styleFrom(
            backgroundColor: Colors.white,
            side: const BorderSide(color: Color(0xFF747775)),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(LoginWidgetConstants.buttonBorderRadius),
            ),
            padding: const EdgeInsets.symmetric(
              horizontal: LoginWidgetConstants.buttonHorizontalPadding
            ),
          ),
          child: isLoading
              ? SizedBox(
                  height: LoginWidgetConstants.googleIconSize,
                  width: LoginWidgetConstants.googleIconSize,
                  child: CircularProgressIndicator(
                    strokeWidth: LoginWidgetConstants.buttonStrokeWidth
                  ),
                )
              : Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SvgPicture.string(
                      _googleSvg, 
                      height: LoginWidgetConstants.googleIconSize, 
                      width: LoginWidgetConstants.googleIconSize
                    ),
                    const SizedBox(width: LoginWidgetConstants.buttonIconSpacing),
                    Expanded(
                      child: Text(
                        text,
                        style: const TextStyle(
                          fontFamily: 'Roboto',
                          fontWeight: FontWeight.w500,
                          fontSize: LoginWidgetConstants.buttonTextSize,
                          color: Color(0xFF1f1f1f),
                        ),
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
        ),
      ),
    );
  }
}

// =======================================================
// = Function : LoginAppDescription
// = Description : Displays the app description text with secondary color styling
// =======================================================
class LoginAppDescription extends StatelessWidget {
  /// The description text to display
  final String description;

  const LoginAppDescription({super.key, required this.description});

  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: description,
      child: Text(
        description,
        textAlign: TextAlign.center,
        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
          color: LavaMailTheme.textSecondaryColor,
        ),
      ),
    );
  }
}

// =======================================================
// = Function : LoginAppTitle
// = Description : Displays the app title with primary color and bold styling
// =======================================================
class LoginAppTitle extends StatelessWidget {
  /// The title text to display
  final String title;

  const LoginAppTitle({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: title,
      header: true,
      child: Text(
        title,
        style: Theme.of(context).textTheme.displayMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: LavaMailTheme.primaryColor,
        ),
      ),
    );
  }
}

// =======================================================
// = Function : LoginErrorMessage
// = Description : Displays error messages in a styled container with error color
// =======================================================
class LoginErrorMessage extends StatelessWidget {
  /// The error message to display
  final String message;

  const LoginErrorMessage({super.key, required this.message});

  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: 'Error: $message',
      child: Container(
        padding: const EdgeInsets.all(LavaMailTheme.spacingM),
        decoration: BoxDecoration(
          color: LavaMailTheme.errorColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(LavaMailTheme.borderRadiusS),
        ),
        child: Text(
          message,
          textAlign: TextAlign.center,
          style: TextStyle(
            color: LavaMailTheme.errorColor,
            fontSize: LoginWidgetConstants.errorTextSize,
          ),
        ),
      ),
    );
  }
}

// =======================================================
// = Function : LoginFadeTransition
// = Description : Provides fade transition animation for login screen elements
// =======================================================
class LoginFadeTransition extends StatelessWidget {
  /// The animation to apply
  final Animation<double> animation;
  
  /// The child widget to animate
  final Widget child;

  const LoginFadeTransition({
    super.key, 
    required this.animation, 
    required this.child
  });

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: animation,
      child: child,
    );
  }
}

// =======================================================
// = Function : LoginFormContent
// = Description : Main login form container that combines all login screen elements
// =======================================================
class LoginFormContent extends StatelessWidget {
  /// The app title to display
  final String appTitle;

  /// The app description to display
  final String appDescription;

  /// The sign in button text
  final String signInText;

  /// Callback function when Google sign in is pressed
  final VoidCallback onSignIn;

  /// Whether the form is in loading state
  final bool isLoading;

  /// Optional error message to display
  final String? errorMessage;

  /// The app version to display
  final String version;

  const LoginFormContent({
    super.key,
    required this.appTitle,
    required this.appDescription,
    required this.signInText,
    required this.onSignIn,
    required this.isLoading,
    this.errorMessage,
    required this.version,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const AppLogo(),
        const SizedBox(height: LavaMailTheme.spacingXL),
        LoginAppTitle(title: appTitle),
        const SizedBox(height: LavaMailTheme.spacingM),
        LoginAppDescription(description: appDescription),
        const SizedBox(height: LavaMailTheme.spacingXXL),
        GoogleSignInButton(
          text: signInText,
          onPressed: onSignIn,
          isLoading: isLoading,
        ),
        if (errorMessage != null) ...[
          const SizedBox(height: LavaMailTheme.spacingM),
          LoginErrorMessage(message: errorMessage!),
        ],
        const SizedBox(height: LavaMailTheme.spacingXXL),
        LoginVersionInfo(version: version),
      ],
    );
  }
}

// =======================================================
// = Function : LoginVersionInfo
// = Description : Displays the app version information with light text styling
// =======================================================
class LoginVersionInfo extends StatelessWidget {
  /// The version number to display
  final String version;

  const LoginVersionInfo({super.key, required this.version});

  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: 'Version $version',
      child: Text(
        'Version $version',
        style: TextStyle(
          color: LavaMailTheme.textLightColor,
          fontSize: LoginWidgetConstants.versionTextSize,
        ),
      ),
    );
  }
}

// =======================================================
// = Function : _googleSvg
// = Description : SVG string constant containing the Google logo for the sign-in button
// =======================================================
const String _googleSvg = '''<svg version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" xmlns:xlink="http://www.w3.org/1999/xlink"><path fill="#EA4335" d="M24 9.5c3.54 0 6.71 1.22 9.21 3.6l6.85-6.85C35.9 2.38 30.47 0 24 0 14.62 0 6.51 5.38 2.56 13.22l7.98 6.19C12.43 13.72 17.74 9.5 24 9.5z"></path><path fill="#4285F4" d="M46.98 24.55c0-1.57-.15-3.09-.38-4.55H24v9.02h12.94c-.58 2.96-2.26 5.48-4.78 7.18l7.73 6c4.51-4.18 7.09-10.36 7.09-17.65z"></path><path fill="#FBBC05" d="M10.53 28.59c-.48-1.45-.76-2.99-.76-4.59s.27-3.14.76-4.59l-7.98-6.19C.92 16.46 0 20.12 0 24c0 3.88.92 7.54 2.56 10.78l7.97-6.19z"></path><path fill="#34A853" d="M24 48c6.48 0 11.93-2.13 15.89-5.81l-7.73-6c-2.15 1.45-4.92 2.3-8.16 2.3-6.26 0-11.57-4.22-13.47-9.91l-7.98 6.19C6.51 42.62 14.62 48 24 48z"></path><path fill="none" d="M0 0h48v48H0z"></path></svg>''';

// GMX sign-in button (round)
class GmxSignInButton extends StatelessWidget {
  final VoidCallback onPressed;
  final bool isLoading;
  const GmxSignInButton({super.key, required this.onPressed, this.isLoading = false});

  @override
  Widget build(BuildContext context) {
    return Semantics(
      button: true,
      enabled: !isLoading,
      label: 'Sign in with GMX',
      child: SizedBox(
        width: 56,
        height: 56,
        child: Material(
          color: Colors.white,
          shape: const CircleBorder(),
          elevation: 2,
          child: InkWell(
            borderRadius: BorderRadius.circular(28),
            onTap: isLoading ? null : onPressed,
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Image.asset(
                'assets/images/gmx_logo.png', // PLACEHOLDER: Add your GMX logo asset here
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) => const Icon(Icons.email, size: 32, color: Colors.blue),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

// =======================================================
// = Function : LoginProviderSelector
// = Description : Modern provider selection with enhanced styling and animations
// =======================================================
class LoginProviderSelector extends StatelessWidget {
  final VoidCallback onGmail;
  final VoidCallback onYahoo;
  final VoidCallback onGmx;
  final VoidCallback onIcloud;
  final bool isLoadingGmail;
  final bool isLoadingYahoo;
  final bool isLoadingGmx;
  final bool isLoadingIcloud;

  const LoginProviderSelector({
    super.key,
    required this.onGmail,
    required this.onYahoo,
    required this.onGmx,
    required this.onIcloud,
    this.isLoadingGmail = false,
    this.isLoadingYahoo = false,
    this.isLoadingGmx = false,
    this.isLoadingIcloud = false,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    return Column(
      children: [
        const SizedBox(height: LavaMailTheme.spacingXL),
        // Modern divider with text
        Container(
          margin: EdgeInsets.symmetric(horizontal: screenWidth * 0.08),
          child: Row(
            children: [
              Expanded(
                child: Container(
                  height: 1,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.transparent,
                        LavaMailTheme.textSecondaryColor.withValues(alpha: 0.3),
                      ],
                    ),
                  ),
                ),
              ),
              Container(
                margin: const EdgeInsets.symmetric(horizontal: LavaMailTheme.spacingM),
                padding: const EdgeInsets.symmetric(
                  horizontal: LavaMailTheme.spacingM,
                  vertical: LavaMailTheme.spacingS,
                ),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Text(
                  'Choose your email provider',
                  style: TextStyle(
                    fontFamily: 'Roboto',
                    fontWeight: FontWeight.w500,
                    fontSize: 13,
                    color: LavaMailTheme.textSecondaryColor,
                    letterSpacing: 0.3,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              Expanded(
                child: Container(
                  height: 1,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        LavaMailTheme.textSecondaryColor.withValues(alpha: 0.3),
                        Colors.transparent,
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: LavaMailTheme.spacingXL),
        // Provider buttons with enhanced styling and proper spacing
        Container(
          padding: const EdgeInsets.symmetric(horizontal: LavaMailTheme.spacingL),
          child: Wrap(
            alignment: WrapAlignment.center,
            spacing: LavaMailTheme.spacingL, // Horizontal spacing between buttons
            runSpacing: LavaMailTheme.spacingM, // Vertical spacing if buttons wrap to next line
            children: [
              LavaMailProviderButton(
                providerName: 'Gmail',
                assetPath: 'assets/images/gmail.png',
                onTap: onGmail,
                isLoading: isLoadingGmail,
              ),
              LavaMailProviderButton(
                providerName: 'Yahoo',
                assetPath: 'assets/images/yahoo.png',
                onTap: onYahoo,
                isLoading: isLoadingYahoo,
              ),
              LavaMailProviderButton(
                providerName: 'GMX',
                assetPath: 'assets/images/gmx.png',
                onTap: onGmx,
                isLoading: isLoadingGmx,
              ),
              LavaMailProviderButton(
                providerName: 'iCloud',
                assetPath: 'assets/images/icloud.png',
                onTap: onIcloud,
                isLoading: isLoadingIcloud,
              ),
            ],
          ),
        ),
      ],
    );
  }
}

