import 'package:flutter/material.dart';
import 'package:google_sign_in/google_sign_in.dart';
import '../../core/gmail/online_mode/gmail_online_mode.dart' as online;

import '../../utils/services/storage/email_preload_service.dart';

import 'package:logger/logger.dart';

class GmailScreenLabels extends StatefulWidget {
  final GoogleSignInAccount user;
  final List<Map<String, dynamic>> labelStats;

  const GmailScreenLabels({
    super.key,
    required this.user,
    required this.labelStats,
  });

  @override
  State<GmailScreenLabels> createState() => _GmailScreenLabelsState();
}

class _GmailScreenLabelsState extends State<GmailScreenLabels> {
  final Logger _logger = Logger();
  bool loading = true;
  String? error;
  List<Map<String, dynamic>> labelStats = [];
  bool _isOnlineMode = false;

  @override
  void initState() {
    super.initState();
    labelStats = widget.labelStats;
    if (labelStats.isEmpty) {
      _loadLabels();
    } else {
      loading = false;
    }
  }

  Future<void> _loadLabels() async {
    setState(() {
      loading = true;
      error = null;
    });

    try {
      // Try to get preloaded data first
      final preloadService = EmailPreloadService();
      final preloadedLabelStats = preloadService.getPreloadedLabelStats();

      if (preloadedLabelStats != null) {
        // Use preloaded data for instant loading
        setState(() {
          labelStats = preloadedLabelStats;
          loading = false;
        });
        return;
      }

      // Fallback to API call if no preloaded data
      _logger.d('[LABELS] Loading labels from ONLINE API');
      final api = online.GmailApiService(widget.user);
      final stats = await api.getUserLabelStats();
      setState(() {
        labelStats = stats;
        loading = false;
      });
    } catch (e) {
      _logger.e('[LABELS] Error loading labels: $e');
      setState(() {
        error = e.toString();
        loading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Labels'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: loading
          ? const Center(child: CircularProgressIndicator())
          : error != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error_outline, size: 64, color: Colors.red),
                      const SizedBox(height: 16),
                      Text(error!, textAlign: TextAlign.center),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadLabels,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : labelStats.isEmpty
                  ? const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.label_outline, size: 64, color: Colors.grey),
                          SizedBox(height: 16),
                          Text(
                            'No user labels found',
                            style: TextStyle(fontSize: 18, color: Colors.grey),
                          ),
                          SizedBox(height: 8),
                          Text(
                            'Create labels in Gmail to see them here',
                            style: TextStyle(color: Colors.grey),
                          ),
                        ],
                      ),
                    )
                  : Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: GridView.builder(
                        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          crossAxisSpacing: 8,
                          mainAxisSpacing: 8,
                          childAspectRatio: 1.7,
                        ),
                        itemCount: labelStats.length,
                        itemBuilder: (context, index) {
                          final label = labelStats[index];
                          final labelName = label['label'] ?? 'Unknown';
                          final count = label['count'] ?? 0;
                          final sizeMb = label['size'] != null
                              ? (label['size'] / (1024 * 1024)).toStringAsFixed(1)
                              : null;

                          return Card(
                            elevation: 2,
                            child: InkWell(
                              onTap: () {
                                // TODO: Navigate to label detail screen
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text('Label "$labelName" details coming soon!'),
                                    duration: const Duration(seconds: 2),
                                  ),
                                );
                              },
                              child: Padding(
                                padding: const EdgeInsets.all(12.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        const Icon(Icons.label, color: Colors.deepPurple, size: 20),
                                        const SizedBox(width: 8),
                                        Expanded(
                                          child: Text(
                                            labelName,
                                            style: const TextStyle(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 14,
                                            ),
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      '$count emails',
                                      style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w500,
                                        color: Colors.blue,
                                      ),
                                    ),
                                    if (sizeMb != null) ...[
                                      const SizedBox(height: 4),
                                      Text(
                                        '${sizeMb}MB',
                                        style: const TextStyle(
                                          fontSize: 12,
                                          color: Colors.grey,
                                        ),
                                      ),
                                    ],
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
    );
  }
}