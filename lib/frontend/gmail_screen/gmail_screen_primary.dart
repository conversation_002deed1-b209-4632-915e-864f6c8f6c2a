import 'package:flutter/material.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../../../core/gmail/online_mode/gmail_online_mode.dart' as online;

import '../../../utils/services/storage/email_preload_service.dart';
import '../theme/app_theme.dart';
import 'widgets/gmail_screen_widgets.dart';
import '../../../utils/helpers/data/email_list_utils.dart';

class GmailScreenPrimary extends StatefulWidget {
  final GoogleSignInAccount user;
  const GmailScreenPrimary({super.key, required this.user});

  @override
  State<GmailScreenPrimary> createState() => _GmailScreenPrimaryState();
}

class _GmailScreenPrimaryState extends State<GmailScreenPrimary> {
  List<Map<String, dynamic>> emails = [];
  List<Map<String, dynamic>> sortedEmails = [];
  List<Map<String, dynamic>> paginatedEmails = [];
  bool loading = true;
  String? error;
  int totalSize = 0;
  int totalCount = 0;

  final GlobalKey<EmailDeleteWidgetState> _deleteWidgetKey = GlobalKey<EmailDeleteWidgetState>();
  final PaginationState _paginationState = PaginationState();

  @override
  void initState() {
    super.initState();
    _loadPrimary();
  }

  Future<void> _loadPrimary() async {
    setState(() { loading = true; error = null; });
    try {
      // Try to get preloaded data first
      final preloadService = EmailPreloadService();
      final preloadedEmails = preloadService.getPreloadedEmails('primary');
      final preloadedStats = preloadService.getPreloadedStats('primary');

      if (preloadedEmails != null && preloadedStats != null) {
        // Use preloaded data for instant loading
        emails = preloadedEmails;
        totalSize = preloadedStats['size'] ?? 0;
        totalCount = preloadedStats['count'] ?? 0;
        sortedEmails = List.from(emails);
        _updatePaginatedEmails();
        setState(() { loading = false; });
        return;
      }

      // Fallback to API call if no preloaded data
      final api = online.GmailApiService(widget.user);
      final stats = await api.getCategoryStats('primary');
      final result = await api.getEmails(
        q: 'in:inbox -category:social -category:promotions -category:updates -category:forums',
        maxResults: 100,
        includeMetadata: true
      );
      emails = List<Map<String, dynamic>>.from(result['messages'] ?? []);
      totalSize = stats['size'] ?? 0;
      totalCount = stats['count'] ?? 0;
      sortedEmails = List.from(emails);
      _updatePaginatedEmails();
      setState(() { loading = false; });
    } catch (e) {
      setState(() { error = e.toString(); loading = false; });
    }
  }

  void _updatePaginatedEmails() {
    paginatedEmails = paginateMessages(sortedEmails, _paginationState);
  }

  Future<void> _deleteEmails(List<String> emailIds) async {
    try {
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Confirmer la suppression'),
          content: Text('Voulez-vous vraiment supprimer ${emailIds.length} email(s) ?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Annuler'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('Supprimer'),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        // Supprimer les emails via l'API Gmail
        final api = online.GmailApiService(widget.user);
        await api.batchDeleteEmails(emailIds);

        // Supprimer de la liste locale après succès de l'API
        setState(() {
          emails.removeWhere((email) => emailIds.contains(email['id']));
          sortedEmails.removeWhere((email) => emailIds.contains(email['id']));
          _updatePaginatedEmails();
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${emailIds.length} email(s) supprimé(s)'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la suppression: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.primary),
      ),
      body: loading
          ? const Center(child: CircularProgressIndicator())
          : error != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.error_outline, size: 64, color: Colors.red),
                      const SizedBox(height: 16),
                      Text(error!, textAlign: TextAlign.center),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadPrimary,
                        child: Text(l10n.retry),
                      ),
                    ],
                  ),
                )
              : Column(
                    children: [
                      // Widget de tri avec groupement
                      Expanded(
                        child: EmailSortWidget(
                          emails: emails,
                          onSorted: (sorted) {
                            setState(() {
                              sortedEmails = sorted;
                            });
                          },
                          themeColor: LavaMailTheme.inboxColor,
                          showGroupedList: true,
                          onEmailsSelected: (selectedEmailIds) {
                            // Handle selected emails from groups
                            if (selectedEmailIds.isNotEmpty) {
                              _deleteEmails(selectedEmailIds);
                            }
                          },
                        ),
                      ),
                      // Widget de suppression
                      EmailDeleteWidget(
                        key: _deleteWidgetKey,
                        emails: emails,
                        onDeleteSelected: _deleteEmails,
                        themeColor: LavaMailTheme.inboxColor,
                      ),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.blue.withValues(alpha: 0.1),
                          border: Border(
                            bottom: BorderSide(color: Colors.blue.withValues(alpha: 0.3)),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            Column(
                              children: [
                                Icon(Icons.inbox, color: Colors.blue),
                                const SizedBox(height: 4),
                                Text(
                                  '$totalCount',
                                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                    color: Colors.blue,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Text(l10n.emails, style: TextStyle(color: Colors.blue)),
                              ],
                            ),
                            Column(
                              children: [
                                Icon(Icons.storage, color: Colors.blue),
                                const SizedBox(height: 4),
                                Text(
                                  '${(totalSize / (1024 * 1024)).toStringAsFixed(1)} MB',
                                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                    color: Colors.blue,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Text(l10n.totalSize, style: TextStyle(color: Colors.blue)),
                              ],
                            ),
                            Column(
                              children: [
                                Icon(Icons.list, color: Colors.blue),
                                const SizedBox(height: 4),
                                Text(
                                  '${emails.length}',
                                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                    color: Colors.blue,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Text("Affichés", style: TextStyle(color: Colors.blue)),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
    );
  }
}
