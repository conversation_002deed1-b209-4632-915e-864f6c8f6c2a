/*
=======================================================
= File: user_provider.dart
= Project: LavaMail
= Description:
=   - Provides a ChangeNotifier for managing the current Google user and related email statistics.
=   - Exposes user info and preloaded mail stats for use throughout the app.
=   - All code, comments, and documentation are in English as per project standards.
=======================================================
*/
import 'package:flutter/material.dart';
import 'package:google_sign_in/google_sign_in.dart';
import '../core/gmx/models/gmx_user.dart';
import '../core/yahoo/models/yahoo_user.dart';
import '../core/icloud/models/icloud_user.dart';

// =======================================================
// = Class : UserProvider
// = Description : Manages the current Google user and preloaded email statistics for the app session.
// =======================================================

// Add enum for user type
enum UserType { gmail, gmx, yahoo, icloud }

class ImapUser {
  final String email;
  ImapUser(this.email);
}

class UserProvider extends ChangeNotifier {
  // =======================================================
  // = Private Properties
  // = Description : Internal state for user and email statistics
  // =======================================================
  GoogleSignInAccount? _user;
  GmxUser? _gmxUser;
  YahooUser? _yahooUser;
  IcloudUser? _icloudUser;
  int? _totalEmailCount;
  Map<String, int>? _categoryCounts;
  int? _totalAttachmentCount;
  UserType _userType = UserType.gmail;
  ImapUser? _imapUser;

  // =======================================================
  // = Getters
  // = Description : Public access to user and email statistics
  // =======================================================
  
  /// The currently signed-in Google user, if any
  GoogleSignInAccount? get user => _user;

  /// The currently signed-in GMX user, if any
  GmxUser? get gmxUser => _gmxUser;

  /// The currently signed-in Yahoo user, if any
  YahooUser? get yahooUser => _yahooUser;

  /// The currently signed-in iCloud user, if any
  IcloudUser? get icloudUser => _icloudUser;

  /// Total number of emails in the user's account
  int? get totalEmailCount => _totalEmailCount;

  /// Map of email counts by category (e.g., 'INBOX': 100, 'SPAM': 5)
  Map<String, int>? get categoryCounts => _categoryCounts?.isNotEmpty == true 
    ? Map<String, int>.unmodifiable(_categoryCounts!) 
    : null;

  /// Total number of attachments across all emails
  int? get totalAttachmentCount => _totalAttachmentCount;

  UserType get userType => _userType;
  set userType(UserType type) {
    _userType = type;
    notifyListeners();
  }

  ImapUser? get imapUser => _imapUser;

  // =======================================================
  // = Function : setUser
  // = Description : Sets the current Google user and notifies listeners.
  // =======================================================
  void setUser(GoogleSignInAccount? user) {
    if (_user?.id != user?.id) {
      _user = user;
      _gmxUser = null; // Clear GMX user when setting Gmail user
      _yahooUser = null; // Clear Yahoo user when setting Gmail user
      _icloudUser = null; // Clear iCloud user when setting Gmail user
      _imapUser = null; // Clear IMAP user when setting Gmail user
      // Clear stats when user changes
      _totalEmailCount = null;
      _categoryCounts = null;
      _totalAttachmentCount = null;
      notifyListeners();
    }
  }

  // =======================================================
  // = Function : setGmxUser
  // = Description : Sets the current GMX user and notifies listeners.
  // =======================================================
  void setGmxUser(GmxUser? user) {
    if (_gmxUser?.email != user?.email) {
      _gmxUser = user;
      _user = null; // Clear Gmail user when setting GMX user
      _yahooUser = null; // Clear Yahoo user when setting GMX user
      _icloudUser = null; // Clear iCloud user when setting GMX user
      _imapUser = null; // Clear IMAP user when setting GMX user
      // Clear stats when user changes
      _totalEmailCount = null;
      _categoryCounts = null;
      _totalAttachmentCount = null;
      notifyListeners();
    }
  }

  // =======================================================
  // = Function : setYahooUser
  // = Description : Sets the current Yahoo user and notifies listeners.
  // =======================================================
  void setYahooUser(YahooUser? user) {
    if (_yahooUser?.email != user?.email) {
      _yahooUser = user;
      _user = null; // Clear Gmail user when setting Yahoo user
      _gmxUser = null; // Clear GMX user when setting Yahoo user
      _icloudUser = null; // Clear iCloud user when setting Yahoo user
      _imapUser = null; // Clear IMAP user when setting Yahoo user
      // Clear stats when user changes
      _totalEmailCount = null;
      _categoryCounts = null;
      _totalAttachmentCount = null;
      notifyListeners();
    }
  }

  // =======================================================
  // = Function : setIcloudUser
  // = Description : Sets the current iCloud user and notifies listeners.
  // =======================================================
  void setIcloudUser(IcloudUser? user) {
    if (_icloudUser?.email != user?.email) {
      _icloudUser = user;
      _user = null; // Clear Gmail user when setting iCloud user
      _gmxUser = null; // Clear GMX user when setting iCloud user
      _yahooUser = null; // Clear Yahoo user when setting iCloud user
      _imapUser = null; // Clear IMAP user when setting iCloud user
      // Clear stats when user changes
      _totalEmailCount = null;
      _categoryCounts = null;
      _totalAttachmentCount = null;
      notifyListeners();
    }
  }

  // =======================================================
  // = Function : setImapUser
  // = Description : Sets the current IMAP user and notifies listeners.
  // =======================================================
  void setImapUser(ImapUser? user) {
    if (_imapUser?.email != user?.email) {
      _imapUser = user;
      _user = null; // Clear Google user when setting IMAP user
      _gmxUser = null; // Clear GMX user when setting IMAP user
      _yahooUser = null; // Clear Yahoo user when setting IMAP user
      _icloudUser = null; // Clear iCloud user when setting IMAP user
      // Clear stats when user changes
      _totalEmailCount = null;
      _categoryCounts = null;
      _totalAttachmentCount = null;
      notifyListeners();
    }
  }

  // =======================================================
  // = Function : clearUser
  // = Description : Clears the current user and notifies listeners.
  // =======================================================
  void clearUser() {
    if (_user != null || _gmxUser != null || _yahooUser != null || _icloudUser != null || _imapUser != null) {
      _user = null;
      _gmxUser = null;
      _yahooUser = null;
      _icloudUser = null;
      _imapUser = null;
      _totalEmailCount = null;
      _categoryCounts = null;
      _totalAttachmentCount = null;
      notifyListeners();
    }
  }

  // =======================================================
  // = Function : setTotalEmailCount
  // = Description : Sets the total email count and notifies listeners.
  // =======================================================
  void setTotalEmailCount(int? count) {
    if (count != _totalEmailCount) {
      _totalEmailCount = count?.clamp(0, double.maxFinite.toInt());
      notifyListeners();
    }
  }

  // =======================================================
  // = Function : setCategoryCounts
  // = Description : Sets the category counts and notifies listeners.
  // =======================================================
  void setCategoryCounts(Map<String, int>? counts) {
    if (counts?.toString() != _categoryCounts?.toString()) {
      _categoryCounts = counts?.map((key, value) => MapEntry(key, value.clamp(0, double.maxFinite.toInt())));
      notifyListeners();
    }
  }

  // =======================================================
  // = Function : setTotalAttachmentCount
  // = Description : Sets the total attachment count and notifies listeners.
  // =======================================================
  void setTotalAttachmentCount(int? count) {
    if (count != _totalAttachmentCount) {
      _totalAttachmentCount = count?.clamp(0, double.maxFinite.toInt());
      notifyListeners();
    }
  }
}
