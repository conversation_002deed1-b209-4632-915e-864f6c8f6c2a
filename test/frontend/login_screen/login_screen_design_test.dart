import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:lavamail/frontend/login_screen/widgets/login_screen_widgets.dart';
import 'package:lavamail/frontend/login_screen/widgets/gmx_login_modal.dart';
import 'package:lavamail/frontend/theme/app_theme.dart';

void main() {
  group('Login Screen Design Tests', () {
    testWidgets('AppLogo displays with modern styling', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: LavaMailTheme.lightTheme(),
          home: const Scaffold(
            body: AppLogo(),
          ),
        ),
      );

      // Verify logo container is present
      expect(find.byType(Container), findsWidgets);
      
      // Verify title is present
      expect(find.text('LAVAMAIL'), findsOneWidget);
      
      // Verify subtitle is present
      expect(find.text('Professional Email Management'), findsOneWidget);
    });

    testWidgets('LoginProviderSelector displays modern provider buttons', (WidgetTester tester) async {
      bool gmailTapped = false;
      bool yahooTapped = false;
      bool gmxTapped = false;
      bool icloudTapped = false;

      await tester.pumpWidget(
        MaterialApp(
          theme: LavaMailTheme.lightTheme(),
          home: Scaffold(
            body: LoginProviderSelector(
              onGmail: () => gmailTapped = true,
              onYahoo: () => yahooTapped = true,
              onGmx: () => gmxTapped = true,
              onIcloud: () => icloudTapped = true,
            ),
          ),
        ),
      );

      // Verify provider selection text is present
      expect(find.text('Choose your email provider'), findsOneWidget);

      // Verify provider names are present
      expect(find.text('Gmail'), findsOneWidget);
      expect(find.text('Yahoo'), findsOneWidget);
      expect(find.text('GMX'), findsOneWidget);
      expect(find.text('iCloud'), findsOneWidget);

      // Test Gmail button tap
      await tester.tap(find.text('Gmail'));
      await tester.pump();
      expect(gmailTapped, isTrue);

      // Test Yahoo button tap
      await tester.tap(find.text('Yahoo'));
      await tester.pump();
      expect(yahooTapped, isTrue);

      // Test GMX button tap
      await tester.tap(find.text('GMX'));
      await tester.pump();
      expect(gmxTapped, isTrue);

      // Test iCloud button tap
      await tester.tap(find.text('iCloud'));
      await tester.pump();
      expect(icloudTapped, isTrue);
    });

    testWidgets('Modern provider buttons have proper styling', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: LavaMailTheme.lightTheme(),
          home: Scaffold(
            body: LoginProviderSelector(
              onGmail: () {},
              onYahoo: () {},
              onGmx: () {},
              onIcloud: () {},
            ),
          ),
        ),
      );

      // Verify containers with proper styling exist
      final containers = tester.widgetList<Container>(find.byType(Container));
      expect(containers.length, greaterThan(0));

      // Verify circular containers for buttons exist
      final decoratedBoxes = containers.where((container) {
        final decoration = container.decoration;
        return decoration is BoxDecoration &&
               decoration.shape == BoxShape.circle;
      });
      expect(decoratedBoxes.length, equals(4)); // Gmail, Yahoo, GMX, iCloud
    });

    testWidgets('Loading state displays correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: LavaMailTheme.lightTheme(),
          home: Scaffold(
            body: LoginProviderSelector(
              onGmail: () {},
              onYahoo: () {},
              onGmx: () {},
              onIcloud: () {},
              isLoadingGmail: true,
            ),
          ),
        ),
      );

      // Verify loading indicator is present
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('Error message displays with proper styling', (WidgetTester tester) async {
      const errorMessage = 'Test error message';
      
      await tester.pumpWidget(
        MaterialApp(
          theme: LavaMailTheme.lightTheme(),
          home: const Scaffold(
            body: LoginErrorMessage(message: errorMessage),
          ),
        ),
      );

      // Verify error message is displayed
      expect(find.text(errorMessage), findsOneWidget);
      
      // Verify error styling container exists
      expect(find.byType(Container), findsOneWidget);
    });

    testWidgets('GMX login form displays correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: LavaMailTheme.lightTheme(),
          home: Scaffold(
            body: GmxLoginForm(
              onBack: () {},
            ),
          ),
        ),
      );

      // Verify login form elements are present
      expect(find.text('GMX Login'), findsOneWidget);
      expect(find.text('Email Address'), findsOneWidget);
      expect(find.text('Password'), findsOneWidget);
      expect(find.text('Advanced Settings'), findsOneWidget);
      expect(find.text('Login to GMX'), findsOneWidget);
    });
  });
}
