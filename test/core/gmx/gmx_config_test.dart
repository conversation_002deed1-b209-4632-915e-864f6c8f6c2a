import 'package:flutter_test/flutter_test.dart';
import 'package:lavamail/core/gmx/models/gmx_user.dart';
import 'package:lavamail/core/gmx/auth/gmx_auth_service.dart';

void main() {
  group('GMX Configuration Tests', () {
    test('GmxUser has correct default server settings', () {
      final user = GmxUser(
        email: '<EMAIL>',
        displayName: 'Test User',
        password: 'password',
      );

      // Verify IMAP settings
      expect(user.imapHost, equals('imap.gmx.com'));
      expect(user.imapPort, equals(993));

      // Verify SMTP settings (should use mail.gmx.com)
      expect(user.smtpHost, equals('mail.gmx.com'));
      expect(user.smtpPort, equals(587));

      // Verify SSL setting
      expect(user.useSSL, isTrue);
    });

    test('GmxUser can be created with custom settings', () {
      final user = GmxUser(
        email: '<EMAIL>',
        displayName: 'Test User',
        password: 'password',
        smtpPort: 465, // SSL/TLS port
      );

      expect(user.smtpPort, equals(465));
    });

    test('GmxAuthService uses correct default parameters', () {
      // This test verifies that the signInWithCredentials method
      // has the correct default parameters
      
      // We can't easily test the actual method without real credentials,
      // but we can verify the method signature exists and has the right defaults
      expect(GmxAuthService.signInWithCredentials, isA<Function>());
    });
  });
}
