# lavamail

A new Flutter project.

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.

## 📦 Dossiers et responsabilités

### `core/gmail_core/`
**But** : Contient toute la logique métier liée à Gmail, indépendante de Flutter.

- **gmail_core.dart** : Point d'entrée du core, expose les principales fonctionnalités.
- **gmail_api.dart** : Gestion des appels à l'API Gmail (HTTP, parsing, erreurs).
- **gmail_sync.dart** : Logique de synchronisation (complète, incrémentale, gestion du progrès, batch, etc.).
- **gmail_cache.dart** : Gestion du cache local (Hive), lecture/écriture des emails, labels, settings.
- **gmail_models.dart** : Modèles de données (Email, Label, SyncSettings, etc.) et adapters Hive.
- **gmail_quota.dart** : Gestion des quotas, retry, throttling.
- **gmail_utils.dart** : Fonctions utilitaires partagées.
- **gmail_exceptions.dart** : Gestion des erreurs/exceptions spécifiques Gmail.
- **gmail_constants.dart** : Constantes (URLs, scopes, etc.).

### `frontend/gmail_ui/`
**But** : Widgets Flutter spécifiques à Gmail (inbox, détail, synchronisation, paramètres).

- **gmail_screen.dart** : Affichage principal de la boîte de réception.
- **gmail_sync_screen.dart** : Widget de synchronisation et progression.
- **gmail_settings_screen.dart** : Paramètres Gmail.
- **gmail_email_detail.dart** : Détail d'un email.

### `frontend/app_ui/`
**But** : UI générale de l'application (hors Gmail).

- **login_screen/** : Écrans et logique de connexion (Google/Firebase).
- **home_screen/** : Accueil après login/synchronisation.

### `main.dart`
**But** : Point d'entrée de l'application Flutter, initialisation et navigation principale.

## 🚀 Principes d'architecture

- **Séparation stricte** entre la logique métier (core) et la présentation (frontend).
- **Réutilisabilité** : le core peut être utilisé dans d'autres apps (CLI, desktop, etc.).
- **Testabilité** : le core peut être testé indépendamment de Flutter.
- **Scalabilité** : facile d'ajouter de nouvelles fonctionnalités ou interfaces.

## 🛠️ Pour contribuer

- Ajoute toute nouvelle logique Gmail dans `core/gmail_core/`.
- Ajoute tout nouveau widget ou écran dans `frontend/gmail_ui/` ou `frontend/app_ui/`.
- Respecte le préfixe `gmail_` pour tous les fichiers du core Gmail.

**Bonne contribution à LavaMail 🚀
