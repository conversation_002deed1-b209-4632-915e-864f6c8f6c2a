### 🟦 **1. Démarrage de l'application (main.dart)**

✅ **Action de l'application**

* Lancement de l'application Flutter via `main.dart`.

---

### 🟦 **2. Affichage de l'écran de démarrage (SplashScreen)**

✅ **Action de l'application**

* Affiche un écran de type Splash pour donner un aperçu rapide pendant le chargement.

---

### 🟦 **3. Initialisation de Hive et Firebase**

✅ **Action de l'application**

* `Hive` : ouverture/création de la base de données locale.
* `Firebase` : configuration des services (auth, base de données, etc.).

---

### 🟩 **4. Vérification du succès de l'initialisation**

✅ **Action de l'application**

* Si tout est bien initialisé :

  * Elle passe à l'écran de connexion (`LoginScreen`).
* Sinon :

  * Elle affiche une erreur et s’arrête.

---

### 🟦 **5. Affichage de l’écran de connexion (LoginScreen)**

✅ **Action de l'application**

* Affiche une interface avec :

  * Un logo
  * Le nom de l’application
  * Un bouton "Continue with Gmail"

---