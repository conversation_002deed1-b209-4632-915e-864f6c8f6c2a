### 🟨 **6. L'utilisateur appuie sur "Continue with G<PERSON>"**

🧑 **Action de l'utilisateur**

* Il déclenche le processus de connexion avec Google.

---

### 🟦 **7. Changement du texte du bouton ("Signing in...")**

✅ **Action de l'application**

* Met à jour le texte du bouton pour indiquer qu’une action est en cours.

---

### 🟦 **8. Appel au module d'authentification**

✅ **Action de l'application**

* Déclenche la logique d'authentification.

---

### 🟩 **9. Vérification si l'utilisateur est déjà connecté**

✅ **Action de l'application**

* Si l'utilisateur est déjà authentifié :

  * Elle récupère les informations de session.
* Sinon :

  * Elle lance Google Sign-In et obtient les identifiants.

---

### 🟦 **10. Connexion à FirebaseAuth avec les identifiants Google**

✅ **Action de l'application**

* Authentifie l’utilisateur auprès de Firebase.

---

### 🟩 **11. Vérification du succès de l'authentification**

✅ **Action de l'application**

* Si c’est un succès :

  * Redirige vers `SyncScreen`.
* Sinon :

  * Affiche une erreur de connexion et arrête.

---

### 🟦 **12. Affichage de l'écran de synchronisation (SyncScreen)**