# Guide de dépannage AdMob - LavaMail

## 🔍 Problème : Les publicités ne s'affichent pas

### Étapes de diagnostic

#### 1. Vérifier les logs
Recherchez ces messages dans les logs de l'application :

```
=== DEBUT AFFICHAGE APP OPEN AD ===
✅ UTILISATION DES IDS DE PRODUCTION ADMOB
=== CHARGEMENT APP OPEN AD ===
✅ AppOpenAdManager: App Open Ad chargée avec SUCCÈS
🚀 AppOpenAdManager: LANCEMENT DE L'AFFICHAGE...
🎯 AppOpenAdManager: ANNONCE AFFICHÉE EN PLEIN ÉCRAN
```

#### 2. Messages d'erreur courants

**❌ "Aucune App Open Ad disponible"**
- L'annonce n'a pas eu le temps de se charger
- Problème de connexion internet
- Quota AdMob dépassé

**❌ "ÉCHEC du chargement"**
- IDs AdMob incorrects
- Application non approuvée dans AdMob Console
- Problème de configuration

**❌ "ÉCHEC AFFICHAGE PLEIN ÉCRAN"**
- Annonce expirée
- Problème de timing

### Solutions

#### Solution 1 : Utiliser les IDs de test
Modifiez `lib/utils/services/admob_service.dart` :
```dart
static const bool _useTestAds = true; // Changer à true
```

#### Solution 2 : Vérifier la configuration AdMob Console
1. Connectez-vous à [AdMob Console](https://apps.admob.com/)
2. Vérifiez que l'application est approuvée
3. Vérifiez les IDs :
   - App ID : `ca-app-pub-6232219534114446~3247303760`
   - Ad Unit ID : `ca-app-pub-6232219534114446/3358968492`

#### Solution 3 : Augmenter les délais
Dans `main.dart`, la méthode `_showAppOpenAdAndNavigate()` attend 3 secondes.
Vous pouvez augmenter ce délai si nécessaire.

#### Solution 4 : Vérifier AndroidManifest.xml
Assurez-vous que l'App ID est correct dans `android/app/src/main/AndroidManifest.xml` :
```xml
<meta-data
    android:name="com.google.android.gms.ads.APPLICATION_ID"
    android:value="ca-app-pub-6232219534114446~3247303760"/>
```

### Test rapide

Exécutez le script de test :
```bash
dart run test_admob.dart
```

### Logs utiles pour le debug

Activez les logs détaillés en ajoutant dans `main.dart` :
```dart
Logger.level = Level.debug;
```

### Fréquence des annonces

Les App Open Ads ont des limitations :
- Maximum 1 annonce par session
- Délai minimum entre les annonces
- Cache de 4 heures maximum

### Revenus et métriques

Vérifiez dans AdMob Console :
- Taux de remplissage
- Impressions
- Revenus
- Erreurs

### Contact support

Si le problème persiste :
1. Vérifiez les logs complets
2. Testez avec les IDs de test
3. Contactez le support AdMob avec les détails de l'erreur
