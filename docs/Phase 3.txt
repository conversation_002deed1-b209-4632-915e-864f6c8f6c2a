### 🟦 **12. Affichage de l'écran de synchronisation (SyncScreen)**

## 🔁 Résumé du processus : **Synchronisation de la boîte Gmail vers la base Hive**

---

### 🟦 **1. Identification du compte Gmail connecté**

✅ **Action de l'application**

* Vérifie quel utilisateur Gmail est actuellement connecté.

---

### 🟦 **2. Vérification de l’existence de la base de données Hive locale**

✅ **Action de l'application**

* Regarde si la base locale (Hive) associée à l’utilisateur existe déjà sur l’appareil.

---

### 🟩 **3. Deux cas possibles selon la base de données :**

#### ✅ Cas A : **La base existe déjà**

* L’application vérifie si une mise à jour est nécessaire.

##### 🔄 Si une mise à jour est nécessaire :

* ✅ **Action de l'application** : Lance une **synchronisation incrémentielle** (mise à jour des changements intervenus depuis la derniere synchronisation uniquement).

##### ✅ Si aucune mise à jour n’est requise :

* ✅ **Action de l'application** : Ignore l’étape de synchronisation et affiche (home_screen).


#### 🚨 Cas B : **La base de données n’existe pas (nouvel utilisateur)**

* ✅ **Action de l'application** :

  * Affiche une **notification** à l’utilisateur que c’est la première synchronisation.
  * Explique que cela peut **prendre du temps**.

🧑 **Action de l’utilisateur (passive)** :

* Comprend que la synchronisation initiale peut être longue (info affichée).

---

### 🟦 **4. Affichage d’un indicateur de progression**

✅ **Action de l'application**

* Affiche :

  * Une **barre de progression**
  * Un **pourcentage d’avancement**
  * Une **estimation du temps restant**

---

### 🟦 **5. Requête à l’API Gmail pour récupérer des données**

✅ **Action de l'application**

* Interroge l’API Gmail pour synchroniser, recuperer et trier :

  * Les **expéditeurs**
  * Les **dates de réception**
  * Les **libellés**
  * Les **spam**
  * Les **catégories**
  * La taille des emails

---

### 🟦 **6. Synchronisation des données dans la base Hive locale**

✅ **Action de l'application**

* Stocke les données extraites dans la base de données Hive (liée au compte Gmail).

---

### 🟦 **7. Affichage du résumé de la synchronisation**

✅ **Action de l'application**

* Affiche un message comme :

  > "345 emails synchronisés"

---

### 🟦 **8. Navigation vers l’écran principal de l’app (`HomeScreen`)**

✅ **Action de l'application**

* Redirige l’utilisateur vers l’interface principale de l’application.