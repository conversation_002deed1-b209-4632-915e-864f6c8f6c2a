# Configuration AdMob pour LavaMail

Ce document explique comment configurer AdMob avec des annonces d'ouverture d'application (App Open Ads) pour Android uniquement.

## Configuration actuelle

### ✅ Conformité avec les recommandations Google
Cette implémentation suit strictement les recommandations officielles de Google AdMob pour Flutter :
- Utilisation d'`AppOpenAdManager` comme classe utilitaire principale
- Gestion des événements via `AppStateEventNotifier.appStateStream`
- Implémentation d'`AppLifecycleReactor` pour écouter les changements d'état
- Gestion correcte de l'expiration des annonces (4 heures)
- Configuration de l'orientation portrait pour les annonces
- Callbacks appropriés pour les événements plein écran

## Configuration actuelle

### 1. Dépendances ajoutées
- `google_mobile_ads: ^6.0.0` dans `pubspec.yaml`

### 2. Configuration Android

#### AndroidManifest.xml
L'App ID AdMob a été configuré dans `android/app/src/main/AndroidManifest.xml` :
```xml
<meta-data
    android:name="com.google.android.gms.ads.APPLICATION_ID"
    android:value="ca-app-pub-6232219534114446~3247303760"/>
```

**✅ Configuré avec votre App ID AdMob de production**

### 3. Services créés

#### AppOpenAdManager (`lib/utils/services/admob_service.dart`)
- Gestionnaire principal des App Open Ads selon les recommandations Google
- Utilise votre Ad Unit ID de production : `ca-app-pub-6232219534114446/3358968492` (lavamail_openAPP)
- Fonctionne uniquement sur Android
- Cache les annonces pendant 4 heures maximum
- Gère automatiquement l'expiration et le rechargement des annonces

#### AppLifecycleReactor (`lib/utils/services/app_lifecycle_manager.dart`)
- Écoute les événements de premier plan via `AppStateEventNotifier`
- Affiche automatiquement les annonces lors du retour à l'application
- Implémentation conforme aux recommandations officielles Google
- Fonctionne uniquement sur Android

## ✅ Configuration de production terminée

### Informations AdMob configurées :
- **App ID** : `ca-app-pub-6232219534114446~3247303760`
- **Ad Unit ID** : `ca-app-pub-6232219534114446/3358968492`
- **Nom du bloc d'annonces** : lavamail_openAPP
- **Format** : Annonce à l'ouverture d'application

### Configuration actuelle :
✅ **AndroidManifest.xml** : App ID de production configuré
✅ **AdMob Service** : Ad Unit ID de production configuré
✅ **Android uniquement** : Conforme aux spécifications
✅ **Pas de contrôle utilisateur** : Annonces automatiques

### Prêt pour la production !
L'application est maintenant configurée avec vos vrais IDs AdMob et prête à générer des revenus publicitaires réels.

## Fonctionnement actuel

### Déclenchement des annonces
1. **Au démarrage de l'application** : Une annonce s'affiche après la vérification de l'utilisateur connecté
2. **Retour à l'application** : Une annonce s'affiche automatiquement lors du passage au premier plan (via `AppStateEventNotifier`)

### Gestion des erreurs
- Si une annonce ne peut pas être chargée, l'application continue normalement
- Les erreurs sont loggées mais n'interrompent pas le flux utilisateur
- Une nouvelle annonce est automatiquement préchargée après chaque affichage

### Limitations
- **Android uniquement** : Aucune annonce sur iOS ou autres plateformes
- **Pas de contrôle utilisateur** : Les annonces s'affichent automatiquement
- **Cache de 4 heures** : Les annonces sont considérées comme périmées après 4 heures
- **Orientation portrait** : Les annonces sont configurées pour l'orientation portrait uniquement

## Revenus et optimisation

### Conseils pour maximiser les revenus
1. **Fréquence** : Ne pas afficher trop d'annonces (respecter les délais)
2. **Expérience utilisateur** : Les annonces ne doivent pas gêner l'utilisation
3. **Ciblage** : Configurer le ciblage dans AdMob Console
4. **Métriques** : Surveiller les performances dans AdMob Console

### Métriques importantes
- **Taux de remplissage** : Pourcentage de demandes d'annonces satisfaites
- **eCPM** : Revenus pour 1000 impressions
- **Taux de clics** : Pourcentage d'utilisateurs qui cliquent sur les annonces

## Dépannage

### Problèmes courants
1. **Annonces ne s'affichent pas** : Vérifier les IDs et la configuration
2. **Erreurs de chargement** : Vérifier la connexion internet et les logs
3. **Revenus faibles** : Optimiser le ciblage et la fréquence

### Logs utiles
Les logs AdMob sont préfixés par `AdMob:` et `AppLifecycleManager:` dans la console.
